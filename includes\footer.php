    </div> <!-- End of main container -->
    
    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p class="text-muted">
                        <?php echo $current_lang === 'ar' ? 'نظام متكامل لإدارة بلاغات الاحتيال وحماية المستخدمين' : 'Comprehensive system for managing fraud reports and protecting users'; ?>
                    </p>
                </div>
                <div class="col-md-3">
                    <h6><?php echo getLang('quick_links'); ?></h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted"><?php echo getLang('home'); ?></a></li>
                        <li><a href="submit_report.php" class="text-muted"><?php echo getLang('submit_report'); ?></a></li>
                        <li><a href="about.php" class="text-muted"><?php echo getLang('about'); ?></a></li>
                        <li><a href="contact.php" class="text-muted"><?php echo getLang('contact'); ?></a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6><?php echo getLang('language'); ?></h6>
                    <ul class="list-unstyled">
                        <li><a href="?lang=ar" class="text-muted"><?php echo getLang('arabic'); ?></a></li>
                        <li><a href="?lang=en" class="text-muted"><?php echo getLang('english'); ?></a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-3">
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="text-muted mb-0">
                        &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. 
                        <?php echo $current_lang === 'ar' ? 'جميع الحقوق محفوظة' : 'All rights reserved'; ?>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Copy link functionality
        function copyLink(url) {
            navigator.clipboard.writeText(url).then(function() {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'toast position-fixed top-0 end-0 m-3';
                toast.style.zIndex = '9999';
                toast.innerHTML = `
                    <div class="toast-header bg-success text-white">
                        <strong class="me-auto"><?php echo getLang('success'); ?></strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        <?php echo getLang('link_copied'); ?>
                    </div>
                `;
                document.body.appendChild(toast);
                
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                // Remove toast after it's hidden
                toast.addEventListener('hidden.bs.toast', function() {
                    document.body.removeChild(toast);
                });
            });
        }
        
        // Confirm delete functionality
        function confirmDelete(message) {
            return confirm(message || '<?php echo getLang('confirm_delete'); ?>');
        }
        
        // Share functionality
        function shareReport(title, url, platform) {
            let shareUrl = '';
            
            switch(platform) {
                case 'whatsapp':
                    shareUrl = `https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
                    break;
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                    break;
            }
            
            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        }
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
        
        // Form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return true;
            
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            return strength;
        }
        
        // Update password strength indicator
        function updatePasswordStrength(input) {
            const strength = checkPasswordStrength(input.value);
            const indicator = document.getElementById('password-strength');
            
            if (indicator) {
                let strengthText = '';
                let strengthClass = '';
                
                switch(strength) {
                    case 0:
                    case 1:
                        strengthText = '<?php echo $current_lang === 'ar' ? 'ضعيف جداً' : 'Very Weak'; ?>';
                        strengthClass = 'bg-danger';
                        break;
                    case 2:
                        strengthText = '<?php echo $current_lang === 'ar' ? 'ضعيف' : 'Weak'; ?>';
                        strengthClass = 'bg-warning';
                        break;
                    case 3:
                        strengthText = '<?php echo $current_lang === 'ar' ? 'متوسط' : 'Medium'; ?>';
                        strengthClass = 'bg-info';
                        break;
                    case 4:
                        strengthText = '<?php echo $current_lang === 'ar' ? 'قوي' : 'Strong'; ?>';
                        strengthClass = 'bg-success';
                        break;
                    case 5:
                        strengthText = '<?php echo $current_lang === 'ar' ? 'قوي جداً' : 'Very Strong'; ?>';
                        strengthClass = 'bg-success';
                        break;
                }
                
                indicator.className = `badge ${strengthClass}`;
                indicator.textContent = strengthText;
            }
        }
    </script>
</body>
</html>
