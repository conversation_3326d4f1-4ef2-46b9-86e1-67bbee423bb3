<?php
require_once 'config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$errors = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request';
    } else {
        // Clean and validate input
        $username = cleanInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Validation
        if (empty($username) || empty($password)) {
            $errors[] = getLang('required_fields');
        } else {
            try {
                $pdo = getDB();
                $stmt = $pdo->prepare("SELECT id, username, password, role FROM users WHERE username = ? OR email = ?");
                $stmt->execute([$username, $username]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user && password_verify($password, $user['password'])) {
                    // Login successful
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['user_role'] = $user['role'];
                    
                    $_SESSION['message'] = getLang('login_success');
                    $_SESSION['message_type'] = 'success';
                    
                    // Redirect to dashboard or intended page
                    $redirect = $_GET['redirect'] ?? 'dashboard.php';
                    header('Location: ' . $redirect);
                    exit;
                } else {
                    $errors[] = getLang('login_error');
                }
            } catch(PDOException $e) {
                $errors[] = getLang('error_occurred');
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-lg-6 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    <?php echo getLang('login'); ?>
                </h4>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <form method="POST" action="" id="loginForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <!-- Username/Email -->
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            <?php echo getLang('username'); ?> / <?php echo getLang('email'); ?>
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                               required
                               placeholder="<?php echo $current_lang === 'ar' ? 'اسم المستخدم أو البريد الإلكتروني' : 'Username or Email'; ?>">
                    </div>
                    
                    <!-- Password -->
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            <?php echo getLang('password'); ?>
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" 
                                   required
                                   placeholder="<?php echo $current_lang === 'ar' ? 'كلمة المرور' : 'Password'; ?>">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye" id="eyeIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Remember Me -->
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            <?php echo $current_lang === 'ar' ? 'تذكرني' : 'Remember me'; ?>
                        </label>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            <?php echo getLang('login'); ?>
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <!-- Links -->
                <div class="text-center">
                    <p class="mb-2">
                        <?php echo $current_lang === 'ar' ? 'ليس لديك حساب؟' : 'Don\'t have an account?'; ?>
                        <a href="register.php" class="text-decoration-none">
                            <?php echo getLang('register'); ?>
                        </a>
                    </p>
                    <p class="mb-0">
                        <a href="forgot_password.php" class="text-decoration-none">
                            <?php echo $current_lang === 'ar' ? 'نسيت كلمة المرور؟' : 'Forgot password?'; ?>
                        </a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Demo Accounts Info -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'حسابات تجريبية' : 'Demo Accounts'; ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><?php echo $current_lang === 'ar' ? 'مدير النظام:' : 'Admin:' ?></h6>
                        <p class="mb-1"><strong><?php echo $current_lang === 'ar' ? 'اسم المستخدم:' : 'Username:' ?></strong> admin</p>
                        <p class="mb-0"><strong><?php echo $current_lang === 'ar' ? 'كلمة المرور:' : 'Password:' ?></strong> admin123</p>
                    </div>
                    <div class="col-md-6">
                        <h6><?php echo $current_lang === 'ar' ? 'مستخدم عادي:' : 'Regular User:' ?></h6>
                        <p class="mb-1"><strong><?php echo $current_lang === 'ar' ? 'اسم المستخدم:' : 'Username:' ?></strong> user</p>
                        <p class="mb-0"><strong><?php echo $current_lang === 'ar' ? 'كلمة المرور:' : 'Password:' ?></strong> user123</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');
    
    if (password.type === 'password') {
        password.type = 'text';
        eyeIcon.className = 'fas fa-eye-slash';
    } else {
        password.type = 'password';
        eyeIcon.className = 'fas fa-eye';
    }
});

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    if (!validateForm('loginForm')) {
        e.preventDefault();
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'ar' ? 'جاري تسجيل الدخول...' : 'Logging in...'; ?>';
    submitBtn.disabled = true;
    
    // Re-enable after 5 seconds (in case of error)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>

<?php include 'includes/footer.php'; ?>
