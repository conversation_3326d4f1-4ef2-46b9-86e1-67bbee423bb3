# نظام بلاغات الاحتيال - Fraud Reports System

نظام متكامل لإدارة بلاغات الاحتيال عبر الإنترنت مبني بلغة PHP مع قاعدة بيانات MySQL، ويدعم اللغتين العربية والإنجليزية.

A comprehensive system for managing online fraud reports built with PHP and MySQL database, supporting both Arabic and English languages.

## 🚀 المميزات - Features

### ✨ المميزات الأساسية - Core Features
- **إدارة البلاغات** - Report Management
- **نظام المستخدمين** - User Management  
- **لوحة إدارة متكاملة** - Comprehensive Admin Panel
- **دعم متعدد اللغات** - Multi-language Support (Arabic/English)
- **تصميم متجاوب** - Responsive Design
- **أمان عالي** - High Security

### 🔒 أنواع البلاغات - Report Types
- **احتيال المواقع** - Website Fraud
- **احتيال وسائل التواصل** - Social Media Fraud
- **احتيال الهاتف** - Phone Fraud

### 📱 واجهة المستخدم - User Interface
- **Bootstrap 5** مع دعم RTL للعربية
- **Font Awesome** للأيقونات
- **تصميم عصري** وجذاب
- **سهولة الاستخدام** على جميع الأجهزة

## 🛠️ المتطلبات - Requirements

### الخادم - Server
- **PHP 8.0+** أو أحدث
- **MySQL 5.7+** أو أحدث
- **Apache** أو **Nginx**
- **mod_rewrite** مفعل

### المتصفح - Browser
- **Chrome** 60+
- **Firefox** 55+
- **Safari** 12+
- **Edge** 79+

## 📦 التثبيت - Installation

### 1. تحميل المشروع - Download Project
```bash
git clone https://github.com/yourusername/fraud-reports-system.git
cd fraud-reports-system
```

### 2. إنشاء قاعدة البيانات - Create Database
```sql
CREATE DATABASE fraud_reports CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. استيراد البيانات - Import Data
```bash
mysql -u root -p fraud_reports < database.sql
```

### 4. تكوين قاعدة البيانات - Configure Database
عدّل ملف `config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'fraud_reports');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 5. إعدادات الخادم - Server Setup
تأكد من أن مجلد `uploads/` قابل للكتابة:
```bash
chmod 755 uploads/
```

## 🔐 الحسابات الافتراضية - Default Accounts

### مدير النظام - Admin
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **البريد الإلكتروني:** <EMAIL>

### مستخدم عادي - Regular User
- **اسم المستخدم:** user  
- **كلمة المرور:** user123
- **البريد الإلكتروني:** <EMAIL>

## 📁 هيكل المشروع - Project Structure

```
fraud-reports-system/
├── admin/                 # لوحة الإدارة - Admin Panel
│   ├── index.php         # الصفحة الرئيسية للإدارة
│   ├── reports.php       # إدارة البلاغات
│   ├── users.php         # إدارة المستخدمين
│   └── settings.php      # الإعدادات
├── includes/             # الملفات المشتركة - Shared Files
│   ├── header.php        # رأس الصفحة
│   └── footer.php        # تذييل الصفحة
├── lang/                 # ملفات الترجمة - Language Files
│   ├── ar.php           # العربية
│   └── en.php           # الإنجليزية
├── uploads/              # المرفقات - Attachments
├── config.php            # إعدادات قاعدة البيانات
├── index.php             # الصفحة الرئيسية
├── login.php             # تسجيل الدخول
├── register.php          # التسجيل
├── submit_report.php     # إرسال بلاغ
├── dashboard.php         # لوحة التحكم
└── database.sql          # قاعدة البيانات
```

## 🌐 الصفحات - Pages

### الصفحات العامة - Public Pages
- **الرئيسية** - Homepage (`index.php`)
- **تسجيل الدخول** - Login (`login.php`)
- **التسجيل** - Registration (`register.php`)
- **إرسال بلاغ** - Submit Report (`submit_report.php`)

### صفحات المستخدم - User Pages
- **لوحة التحكم** - Dashboard (`dashboard.php`)
- **الملف الشخصي** - Profile (`profile.php`)
- **تغيير كلمة المرور** - Change Password (`change_password.php`)

### صفحات الإدارة - Admin Pages
- **لوحة الإدارة** - Admin Panel (`admin/index.php`)
- **إدارة البلاغات** - Manage Reports (`admin/reports.php`)
- **إدارة المستخدمين** - Manage Users (`admin/users.php`)
- **الإعدادات** - Settings (`admin/settings.php`)

## 🔧 الإعدادات - Configuration

### إعدادات قاعدة البيانات - Database Settings
```php
// config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'fraud_reports');
define('DB_USER', 'username');
define('DB_PASS', 'password');
```

### إعدادات الموقع - Site Settings
```php
define('SITE_NAME', 'نظام بلاغات الاحتيال');
define('SITE_URL', 'http://yourdomain.com');
define('DEFAULT_LANG', 'ar');
```

## 🚀 الاستخدام - Usage

### للمستخدمين - For Users
1. **التسجيل** في النظام
2. **تسجيل الدخول** بحسابك
3. **إرسال بلاغ** جديد
4. **متابعة** حالة بلاغاتك
5. **مشاركة** البلاغات مع الآخرين

### للمديرين - For Administrators
1. **تسجيل الدخول** بحساب المدير
2. **مراجعة** البلاغات الجديدة
3. **إدارة** المستخدمين
4. **تحديث** حالة البلاغات
5. **متابعة** الإحصائيات

## 🔒 الأمان - Security

### ميزات الأمان - Security Features
- **CSRF Protection** - حماية من هجمات CSRF
- **SQL Injection Prevention** - منع حقن SQL
- **Password Hashing** - تشفير كلمات المرور
- **Session Management** - إدارة الجلسات
- **Input Validation** - التحقق من المدخلات
- **XSS Protection** - حماية من XSS

### أفضل الممارسات - Best Practices
- استخدام **Prepared Statements**
- تشفير **كلمات المرور** بـ `password_hash()`
- التحقق من **الصلاحيات** قبل كل عملية
- تنظيف **المدخلات** قبل المعالجة

## 🌍 دعم اللغات - Language Support

### العربية - Arabic
- دعم كامل للغة العربية
- اتجاه RTL (من اليمين لليسار)
- نصوص مترجمة بالكامل
- واجهة مخصصة للعربية

### الإنجليزية - English
- دعم كامل للغة الإنجليزية
- اتجاه LTR (من اليسار لليمين)
- نصوص مترجمة بالكامل
- واجهة مخصصة للإنجليزية

## 📱 التصميم المتجاوب - Responsive Design

### الأجهزة المدعومة - Supported Devices
- **أجهزة الكمبيوتر** - Desktop Computers
- **الأجهزة اللوحية** - Tablets
- **الهواتف الذكية** - Smartphones
- **جميع أحجام الشاشات** - All Screen Sizes

### التقنيات المستخدمة - Technologies Used
- **Bootstrap 5** للتصميم
- **CSS3** للتنسيق
- **JavaScript** للتفاعل
- **Font Awesome** للأيقونات

## 🔧 التخصيص - Customization

### تغيير الألوان - Change Colors
عدّل ملفات CSS في `includes/header.php`:
```css
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}
```

### إضافة لغات جديدة - Add New Languages
1. أنشئ ملف جديد في مجلد `lang/`
2. أضف الترجمات المطلوبة
3. عدّل `includes/header.php` لإضافة اللغة الجديدة

## 📊 الإحصائيات - Statistics

### إحصائيات البلاغات - Report Statistics
- إجمالي البلاغات
- البلاغات حسب النوع
- البلاغات حسب الحالة
- البلاغات العاجلة

### إحصائيات المستخدمين - User Statistics
- إجمالي المستخدمين
- المستخدمين النشطين
- المستخدمين الجدد

## 🚀 النشر - Deployment

### على الخادم المحلي - Local Server
1. استخدم **XAMPP** أو **WAMP**
2. انسخ الملفات إلى مجلد `htdocs`
3. أنشئ قاعدة البيانات
4. عدّل الإعدادات

### على الخادم البعيد - Remote Server
1. ارفع الملفات عبر **FTP**
2. أنشئ قاعدة البيانات
3. عدّل `config.php`
4. تأكد من الصلاحيات

## 🐛 استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة - Common Issues

#### خطأ في الاتصال بقاعدة البيانات
```php
// تأكد من صحة البيانات في config.php
define('DB_HOST', 'localhost');
define('DB_USER', 'username');
define('DB_PASS', 'password');
```

#### مشكلة في الصلاحيات
```bash
chmod 755 uploads/
chmod 644 *.php
```

#### مشكلة في اللغة
تأكد من وجود ملفات الترجمة في مجلد `lang/`

## 🤝 المساهمة - Contributing

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. **Fork** المشروع
2. أنشئ **Branch** جديد (`git checkout -b feature/AmazingFeature`)
3. **Commit** التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. **Push** إلى Branch (`git push origin feature/AmazingFeature`)
5. أنشئ **Pull Request**

## 📄 الترخيص - License

هذا المشروع مرخص تحت رخصة **MIT**. راجع ملف `LICENSE` للتفاصيل.

## 📞 الدعم - Support

### طرق التواصل - Contact Methods
- **البريد الإلكتروني:** <EMAIL>
- **GitHub Issues:** [إنشاء Issue جديد](https://github.com/yourusername/fraud-reports-system/issues)
- **الوثائق:** [راجع الوثائق](https://docs.fraudreports.com)

### الموارد المفيدة - Useful Resources
- [دليل PHP](https://www.php.net/manual/)
- [دليل MySQL](https://dev.mysql.com/doc/)
- [دليل Bootstrap](https://getbootstrap.com/docs/)

## 🔄 التحديثات - Updates

### الإصدار الحالي - Current Version
**v1.0.0** - الإصدار الأول

### سجل التحديثات - Changelog
- **v1.0.0** - الإصدار الأول مع جميع المميزات الأساسية

## 🙏 الشكر والتقدير - Acknowledgments

- **Bootstrap** للتصميم
- **Font Awesome** للأيقونات
- **PHP** كلغة برمجة
- **MySQL** لقاعدة البيانات

---

**ملاحظة:** هذا النظام مصمم للأغراض التعليمية والتجارية. يرجى اختباره جيداً قبل الاستخدام في البيئة الإنتاجية.

**Note:** This system is designed for educational and commercial purposes. Please test it thoroughly before using it in production environment.
