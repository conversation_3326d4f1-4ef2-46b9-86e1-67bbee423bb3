<?php
require_once 'config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if it's an AJAX request
if (!isAjaxRequest()) {
    errorResponse('Invalid request method', 400);
}

// Get action
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_reports':
            if (!hasPermission('submit_report')) {
                errorResponse('Unauthorized', 403);
            }
            
            $page = max(1, (int)($_GET['page'] ?? 1));
            $per_page = 10;
            $offset = ($page - 1) * $per_page;
            
            $pdo = getDB();
            $stmt = $pdo->prepare("
                SELECT r.*, u.username 
                FROM reports r 
                LEFT JOIN users u ON r.user_id = u.id 
                ORDER BY r.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$per_page, $offset]);
            $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Format dates
            foreach ($reports as &$report) {
                $report['created_at_formatted'] = timeAgo($report['created_at']);
                $report['type_label'] = getLang($report['type'] . '_fraud');
                $report['status_label'] = getLang($report['status']);
            }
            
            successResponse('Reports retrieved successfully', $reports);
            break;
            
        case 'get_user_stats':
            if (!isset($_SESSION['user_id'])) {
                errorResponse('Unauthorized', 403);
            }
            
            $pdo = getDB();
            $user_id = $_SESSION['user_id'];
            
            // Get user statistics
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $total_reports = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ? AND status = 'pending'");
            $stmt->execute([$user_id]);
            $pending_reports = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ? AND status = 'reviewed'");
            $stmt->execute([$user_id]);
            $reviewed_reports = $stmt->fetchColumn();
            
            $stats = [
                'total_reports' => $total_reports,
                'pending_reports' => $pending_reports,
                'reviewed_reports' => $reviewed_reports
            ];
            
            successResponse('Statistics retrieved successfully', $stats);
            break;
            
        case 'search_reports':
            if (!hasPermission('submit_report')) {
                errorResponse('Unauthorized', 403);
            }
            
            $search = cleanInput($_GET['q'] ?? '');
            if (empty($search)) {
                errorResponse('Search query is required', 400);
            }
            
            $pdo = getDB();
            $stmt = $pdo->prepare("
                SELECT r.*, u.username 
                FROM reports r 
                LEFT JOIN users u ON r.user_id = u.id 
                WHERE r.title LIKE ? OR r.description LIKE ? 
                ORDER BY r.created_at DESC 
                LIMIT 10
            ");
            $stmt->execute(["%$search%", "%$search%"]);
            $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            successResponse('Search completed', $reports);
            break;
            
        case 'get_notifications':
            if (!isset($_SESSION['user_id'])) {
                errorResponse('Unauthorized', 403);
            }
            
            $pdo = getDB();
            $user_id = $_SESSION['user_id'];
            
            $stmt = $pdo->prepare("
                SELECT * FROM notifications 
                WHERE user_id = ? AND is_read = FALSE 
                ORDER BY created_at DESC 
                LIMIT 10
            ");
            $stmt->execute([$user_id]);
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            successResponse('Notifications retrieved successfully', $notifications);
            break;
            
        case 'mark_notification_read':
            if (!isset($_SESSION['user_id'])) {
                errorResponse('Unauthorized', 403);
            }
            
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            if (!$notification_id) {
                errorResponse('Notification ID is required', 400);
            }
            
            $pdo = getDB();
            $user_id = $_SESSION['user_id'];
            
            $stmt = $pdo->prepare("
                UPDATE notifications 
                SET is_read = TRUE 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$notification_id, $user_id]);
            
            if ($stmt->rowCount() > 0) {
                successResponse('Notification marked as read');
            } else {
                errorResponse('Notification not found', 404);
            }
            break;
            
        case 'get_report_comments':
            if (!hasPermission('submit_report')) {
                errorResponse('Unauthorized', 403);
            }
            
            $report_id = (int)($_GET['report_id'] ?? 0);
            if (!$report_id) {
                errorResponse('Report ID is required', 400);
            }
            
            $pdo = getDB();
            $stmt = $pdo->prepare("
                SELECT c.*, u.username, u.role 
                FROM report_comments c 
                JOIN users u ON c.user_id = u.id 
                WHERE c.report_id = ? 
                ORDER BY c.created_at ASC
            ");
            $stmt->execute([$report_id]);
            $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Format dates
            foreach ($comments as &$comment) {
                $comment['created_at_formatted'] = timeAgo($comment['created_at']);
            }
            
            successResponse('Comments retrieved successfully', $comments);
            break;
            
        case 'add_comment':
            if (!isset($_SESSION['user_id'])) {
                errorResponse('Unauthorized', 403);
            }
            
            $report_id = (int)($_POST['report_id'] ?? 0);
            $comment_text = cleanInput($_POST['comment'] ?? '');
            
            if (!$report_id || empty($comment_text)) {
                errorResponse('Report ID and comment are required', 400);
            }
            
            $pdo = getDB();
            $user_id = $_SESSION['user_id'];
            $is_admin = $_SESSION['user_role'] === 'admin';
            
            $stmt = $pdo->prepare("
                INSERT INTO report_comments (report_id, user_id, comment, is_admin_comment) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$report_id, $user_id, $comment_text, $is_admin]);
            
            // Log activity
            logActivity($user_id, 'comment_added', "Added comment to report #$report_id");
            
            successResponse('Comment added successfully');
            break;
            
        case 'get_system_stats':
            if (!hasPermission('admin_panel')) {
                errorResponse('Unauthorized', 403);
            }
            
            $pdo = getDB();
            
            // Get system statistics
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports");
            $total_reports = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'user'");
            $total_users = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'pending'");
            $pending_reports = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'reviewed'");
            $reviewed_reports = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'closed'");
            $closed_reports = $stmt->fetchColumn();
            
            // Get reports by type
            $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM reports GROUP BY type");
            $reports_by_type = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $stats = [
                'total_reports' => $total_reports,
                'total_users' => $total_users,
                'pending_reports' => $pending_reports,
                'reviewed_reports' => $reviewed_reports,
                'closed_reports' => $closed_reports,
                'reports_by_type' => $reports_by_type
            ];
            
            successResponse('System statistics retrieved successfully', $stats);
            break;
            
        default:
            errorResponse('Invalid action', 400);
            break;
    }
    
} catch (Exception $e) {
    errorResponse('Server error: ' . $e->getMessage(), 500);
}
?>
