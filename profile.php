<?php
require_once 'config.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

$pdo = getDB();
$user = [];
$message = '';
$message_type = '';

// Get user data
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $message = 'Error loading user data';
    $message_type = 'danger';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            $username = cleanInput($_POST['username'] ?? '');
            $email = cleanInput($_POST['email'] ?? '');
            
            // Validate input
            if (empty($username) || empty($email)) {
                $message = getLang('required_fields');
                $message_type = 'danger';
            } elseif (!isValidEmail($email)) {
                $message = getLang('invalid_email');
                $message_type = 'danger';
            } else {
                try {
                    // Check if username/email already exists (excluding current user)
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
                    $stmt->execute([$username, $email, $_SESSION['user_id']]);
                    
                    if ($stmt->fetch()) {
                        $message = getLang('username_exists');
                        $message_type = 'danger';
                    } else {
                        // Update user profile
                        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ? WHERE id = ?");
                        $stmt->execute([$username, $email, $_SESSION['user_id']]);
                        
                        // Update session
                        $_SESSION['username'] = $username;
                        
                        $message = getLang('update_success');
                        $message_type = 'success';
                        
                        // Log activity
                        logActivity($_SESSION['user_id'], 'profile_updated', 'Profile information updated');
                        
                        // Refresh user data
                        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $user = $stmt->fetch(PDO::FETCH_ASSOC);
                    }
                } catch(PDOException $e) {
                    $message = getLang('update_error');
                    $message_type = 'danger';
                }
            }
        } elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            // Validate input
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $message = getLang('required_fields');
                $message_type = 'danger';
            } elseif ($new_password !== $confirm_password) {
                $message = getLang('password_mismatch');
                $message_type = 'danger';
            } elseif (strlen($new_password) < 6) {
                $message = getLang('weak_password');
                $message_type = 'danger';
            } else {
                try {
                    // Verify current password
                    $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                    $current_hash = $stmt->fetchColumn();
                    
                    if (password_verify($current_password, $current_hash)) {
                        // Update password
                        $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                        $stmt->execute([$new_hash, $_SESSION['user_id']]);
                        
                        $message = getLang('update_success');
                        $message_type = 'success';
                        
                        // Log activity
                        logActivity($_SESSION['user_id'], 'password_changed', 'Password changed');
                        
                        // Clear password fields
                        $current_password = $new_password = $confirm_password = '';
                    } else {
                        $message = getLang('login_error');
                        $message_type = 'danger';
                    }
                } catch(PDOException $e) {
                    $message = getLang('update_error');
                    $message_type = 'danger';
                }
            }
        }
    } else {
        $message = 'Invalid security token';
        $message_type = 'danger';
    }
}

include 'includes/header.php';
?>

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    <?php echo getLang('profile'); ?>
                </h4>
            </div>
        </div>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- Profile Information -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'معلومات الملف الشخصي' : 'Profile Information'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label"><?php echo getLang('username'); ?></label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label"><?php echo getLang('email'); ?></label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label"><?php echo $current_lang === 'ar' ? 'الدور' : 'Role'; ?></label>
                        <input type="text" class="form-control" value="<?php echo ucfirst($user['role'] ?? ''); ?>" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label"><?php echo $current_lang === 'ar' ? 'تاريخ التسجيل' : 'Registration Date'; ?></label>
                        <input type="text" class="form-control" value="<?php echo date('Y-m-d H:i:s', strtotime($user['created_at'] ?? '')); ?>" readonly>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        <?php echo $current_lang === 'ar' ? 'تحديث الملف الشخصي' : 'Update Profile'; ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Change Password -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'تغيير كلمة المرور' : 'Change Password'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">
                            <?php echo $current_lang === 'ar' ? 'كلمة المرور الحالية' : 'Current Password'; ?>
                        </label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">
                            <?php echo $current_lang === 'ar' ? 'كلمة المرور الجديدة' : 'New Password'; ?>
                        </label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               minlength="6" required>
                        <div class="form-text">
                            <?php echo $current_lang === 'ar' ? 'يجب أن تكون 6 أحرف على الأقل' : 'Must be at least 6 characters'; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">
                            <?php echo $current_lang === 'ar' ? 'تأكيد كلمة المرور' : 'Confirm Password'; ?>
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>
                        <?php echo $current_lang === 'ar' ? 'تغيير كلمة المرور' : 'Change Password'; ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Account Security -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'أمان الحساب' : 'Account Security'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><?php echo $current_lang === 'ar' ? 'آخر تسجيل دخول' : 'Last Login'; ?></h6>
                        <p class="text-muted">
                            <?php echo $current_lang === 'ar' ? 'غير متوفر حالياً' : 'Not available yet'; ?>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><?php echo $current_lang === 'ar' ? 'عنوان IP' : 'IP Address'; ?></h6>
                        <p class="text-muted"><?php echo getClientIP(); ?></p>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $current_lang === 'ar' ? 'حذف الحساب' : 'Delete Account'; ?>
                        </h6>
                        <p class="text-muted mb-0">
                            <?php echo $current_lang === 'ar' 
                                ? 'هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بحسابك.' 
                                : 'This action cannot be undone. All data associated with your account will be deleted.'; ?>
                        </p>
                    </div>
                    <button type="button" class="btn btn-outline-danger" onclick="confirmDeleteAccount()">
                        <i class="fas fa-trash me-2"></i>
                        <?php echo $current_lang === 'ar' ? 'حذف الحساب' : 'Delete Account'; ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeleteAccount() {
    if (confirm('<?php echo $current_lang === 'ar' ? 'هل أنت متأكد من حذف حسابك؟ لا يمكن التراجع عن هذا الإجراء.' : 'Are you sure you want to delete your account? This action cannot be undone.'; ?>')) {
        if (confirm('<?php echo $current_lang === 'ar' ? 'هذا هو التحذير الأخير. هل أنت متأكد تماماً؟' : 'This is your final warning. Are you absolutely sure?'; ?>')) {
            // Redirect to account deletion page
            window.location.href = 'delete_account.php';
        }
    }
}

// Password strength indicator
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strength = checkPasswordStrength(password);
    updatePasswordStrengthIndicator(strength);
});

function checkPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    if (score < 2) return 'weak';
    if (score < 4) return 'medium';
    return 'strong';
}

function updatePasswordStrengthIndicator(strength) {
    const indicator = document.getElementById('password-strength');
    if (!indicator) return;
    
    const colors = {
        weak: 'danger',
        medium: 'warning',
        strong: 'success'
    };
    
    const texts = {
        weak: '<?php echo $current_lang === 'ar' ? 'ضعيف' : 'Weak'; ?>',
        medium: '<?php echo $current_lang === 'ar' ? 'متوسط' : 'Medium'; ?>',
        strong: '<?php echo $current_lang === 'ar' ? 'قوي' : 'Strong'; ?>'
    };
    
    indicator.className = `badge bg-${colors[strength]}`;
    indicator.textContent = texts[strength];
}
</script>

<?php include 'includes/footer.php'; ?>
