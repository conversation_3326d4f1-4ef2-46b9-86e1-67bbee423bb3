<?php
require_once 'config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$errors = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request';
    } else {
        // Clean and validate input
        $username = cleanInput($_POST['username'] ?? '');
        $email = cleanInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
            $errors[] = getLang('required_fields');
        } else {
            // Username validation
            if (strlen($username) < 3 || strlen($username) > 50) {
                $errors[] = $current_lang === 'ar' ? 'اسم المستخدم يجب أن يكون بين 3 و 50 حرف' : 'Username must be between 3 and 50 characters';
            }
            
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
                $errors[] = $current_lang === 'ar' ? 'اسم المستخدم يمكن أن يحتوي على أحرف وأرقام وشرطة سفلية فقط' : 'Username can only contain letters, numbers and underscores';
            }
            
            // Email validation
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = getLang('invalid_email');
            }
            
            // Password validation
            if (strlen($password) < 6) {
                $errors[] = getLang('weak_password');
            }
            
            if ($password !== $confirm_password) {
                $errors[] = getLang('password_mismatch');
            }
            
            // Check if username or email already exists
            if (empty($errors)) {
                try {
                    $pdo = getDB();
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
                    $stmt->execute([$username, $email]);
                    
                    if ($stmt->fetchColumn() > 0) {
                        $errors[] = getLang('username_exists') . ' ' . getLang('or') . ' ' . getLang('email_exists');
                    }
                } catch(PDOException $e) {
                    $errors[] = getLang('error_occurred');
                }
            }
            
            // If no errors, create the user
            if (empty($errors)) {
                try {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, 'user')");
                    $stmt->execute([$username, $email, $hashed_password]);
                    
                    $_SESSION['message'] = getLang('register_success');
                    $_SESSION['message_type'] = 'success';
                    
                    // Redirect to login page
                    header('Location: login.php');
                    exit;
                    
                } catch(PDOException $e) {
                    $errors[] = getLang('register_error');
                }
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-lg-6 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    <?php echo getLang('register'); ?>
                </h4>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <form method="POST" action="" id="registerForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <!-- Username -->
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            <?php echo getLang('username'); ?> *
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                               required minlength="3" maxlength="50"
                               pattern="[a-zA-Z0-9_]+"
                               placeholder="<?php echo $current_lang === 'ar' ? 'اسم المستخدم (3-50 حرف)' : 'Username (3-50 characters)'; ?>">
                        <div class="form-text">
                            <?php echo $current_lang === 'ar' ? 'يمكن استخدام أحرف وأرقام وشرطة سفلية فقط' : 'Only letters, numbers and underscores allowed'; ?>
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            <?php echo getLang('email'); ?> *
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                               required
                               placeholder="<?php echo $current_lang === 'ar' ? 'البريد الإلكتروني' : 'Email address'; ?>">
                    </div>
                    
                    <!-- Password -->
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            <?php echo getLang('password'); ?> *
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" 
                                   required minlength="6"
                                   placeholder="<?php echo $current_lang === 'ar' ? 'كلمة المرور (6 أحرف على الأقل)' : 'Password (minimum 6 characters)'; ?>"
                                   oninput="updatePasswordStrength(this)">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye" id="eyeIcon"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <span id="password-strength" class="badge bg-secondary"><?php echo $current_lang === 'ar' ? 'ضعيف جداً' : 'Very Weak'; ?></span>
                        </div>
                    </div>
                    
                    <!-- Confirm Password -->
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            <?php echo getLang('confirm_password'); ?> *
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               required
                               placeholder="<?php echo $current_lang === 'ar' ? 'تأكيد كلمة المرور' : 'Confirm password'; ?>">
                        <div class="form-text" id="password-match">
                            <?php echo $current_lang === 'ar' ? 'يجب أن تتطابق كلمتا المرور' : 'Passwords must match'; ?>
                        </div>
                    </div>
                    
                    <!-- Terms and Conditions -->
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">
                            <?php echo $current_lang === 'ar' 
                                ? 'أوافق على <a href="terms.php" target="_blank">الشروط والأحكام</a>' 
                                : 'I agree to the <a href="terms.php" target="_blank">Terms and Conditions</a>'; ?>
                        </label>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            <?php echo getLang('register'); ?>
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <!-- Links -->
                <div class="text-center">
                    <p class="mb-0">
                        <?php echo $current_lang === 'ar' ? 'لديك حساب بالفعل؟' : 'Already have an account?'; ?>
                        <a href="login.php" class="text-decoration-none">
                            <?php echo getLang('login'); ?>
                        </a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Password Requirements -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'متطلبات كلمة المرور' : 'Password Requirements'; ?>
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><?php echo $current_lang === 'ar' ? '6 أحرف على الأقل' : 'Minimum 6 characters'; ?></li>
                    <li><?php echo $current_lang === 'ar' ? 'يُنصح باستخدام أحرف كبيرة وصغيرة' : 'Use uppercase and lowercase letters'; ?></li>
                    <li><?php echo $current_lang === 'ar' ? 'يُنصح باستخدام أرقام' : 'Include numbers'; ?></li>
                    <li><?php echo $current_lang === 'ar' ? 'يُنصح باستخدام رموز خاصة' : 'Include special characters'; ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');
    
    if (password.type === 'password') {
        password.type = 'text';
        eyeIcon.className = 'fas fa-eye-slash';
    } else {
        password.type = 'password';
        eyeIcon.className = 'fas fa-eye';
    }
});

// Password confirmation check
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    const matchText = document.getElementById('password-match');
    
    if (confirmPassword === '') {
        matchText.textContent = '<?php echo $current_lang === 'ar' ? 'يجب أن تتطابق كلمتا المرور' : 'Passwords must match'; ?>';
        matchText.className = 'form-text';
    } else if (password === confirmPassword) {
        matchText.textContent = '<?php echo $current_lang === 'ar' ? 'كلمات المرور متطابقة' : 'Passwords match'; ?>';
        matchText.className = 'form-text text-success';
    } else {
        matchText.textContent = '<?php echo $current_lang === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match'; ?>';
        matchText.className = 'form-text text-danger';
    }
});

// Form validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    if (!validateForm('registerForm')) {
        e.preventDefault();
        return false;
    }
    
    // Check password match
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('<?php echo $current_lang === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match'; ?>');
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo $current_lang === 'ar' ? 'جاري التسجيل...' : 'Registering...'; ?>';
    submitBtn.disabled = true;
    
    // Re-enable after 5 seconds (in case of error)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>

<?php include 'includes/footer.php'; ?>
