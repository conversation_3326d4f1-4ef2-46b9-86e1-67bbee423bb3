<?php
require_once '../config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "../lang/{$current_lang}.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $action = $_POST['action'] ?? '';
        $report_id = (int)($_POST['report_id'] ?? 0);
        
        if ($action === 'update_status' && $report_id > 0) {
            $new_status = $_POST['status'] ?? '';
            if (in_array($new_status, ['pending', 'reviewed', 'closed'])) {
                try {
                    $pdo = getDB();
                    $stmt = $pdo->prepare("UPDATE reports SET status = ? WHERE id = ?");
                    $stmt->execute([$new_status, $report_id]);
                    
                    $_SESSION['message'] = getLang('update_success');
                    $_SESSION['message_type'] = 'success';
                } catch(PDOException $e) {
                    $_SESSION['message'] = getLang('update_error');
                    $_SESSION['message_type'] = 'danger';
                }
            }
        } elseif ($action === 'delete' && $report_id > 0) {
            try {
                $pdo = getDB();
                $stmt = $pdo->prepare("DELETE FROM reports WHERE id = ?");
                $stmt->execute([$report_id]);
                
                $_SESSION['message'] = getLang('delete_success');
                $_SESSION['message_type'] = 'success';
            } catch(PDOException $e) {
                $_SESSION['message'] = getLang('delete_error');
                $_SESSION['message_type'] = 'danger';
            }
        }
        
        header('Location: reports.php');
        exit;
    }
}

// Get filters
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';
$search = $_GET['search'] ?? '';

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "r.status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "r.type = ?";
    $params[] = $type_filter;
}

if ($search) {
    $where_conditions[] = "(r.title LIKE ? OR r.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get reports
$pdo = getDB();
$reports = [];

try {
    $query = "
        SELECT r.*, u.username 
        FROM reports r 
        LEFT JOIN users u ON r.user_id = u.id 
        $where_clause
        ORDER BY r.created_at DESC
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(PDOException $e) {
    // Handle error silently for now
}

?>
<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $current_lang === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getLang('reports'); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap RTL CSS for Arabic -->
    <?php if ($current_lang === 'ar'): ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: <?php echo $current_lang === 'ar' ? 'Cairo, Arial, sans-serif' : 'system-ui, sans-serif'; ?>;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-shield-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    <?php echo $_SESSION['username']; ?> (<?php echo getLang('admin_panel'); ?>)
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    <?php echo getLang('logout'); ?>
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    <?php echo getLang('reports'); ?>
                </h4>
                <a href="../index.php" class="btn btn-light btn-sm">
                    <i class="fas fa-home me-1"></i>
                    <?php echo getLang('home'); ?>
                </a>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" action="" class="row g-3 mb-4">
                    <div class="col-md-3">
                        <label for="status" class="form-label"><?php echo getLang('status'); ?></label>
                        <select class="form-select" id="status" name="status">
                            <option value=""><?php echo getLang('all_statuses'); ?></option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>
                                <?php echo getLang('pending'); ?>
                            </option>
                            <option value="reviewed" <?php echo $status_filter === 'reviewed' ? 'selected' : ''; ?>>
                                <?php echo getLang('reviewed'); ?>
                            </option>
                            <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>
                                <?php echo getLang('closed'); ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label"><?php echo getLang('report_type'); ?></label>
                        <select class="form-select" id="type" name="type">
                            <option value=""><?php echo getLang('all_types'); ?></option>
                            <option value="site" <?php echo $type_filter === 'site' ? 'selected' : ''; ?>>
                                <?php echo getLang('site_fraud'); ?>
                            </option>
                            <option value="social" <?php echo $type_filter === 'social' ? 'selected' : ''; ?>>
                                <?php echo getLang('social_fraud'); ?>
                            </option>
                            <option value="phone" <?php echo $type_filter === 'phone' ? 'selected' : ''; ?>>
                                <?php echo getLang('phone_fraud'); ?>
                            </option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label"><?php echo getLang('search'); ?></label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="<?php echo $current_lang === 'ar' ? 'البحث في البلاغات...' : 'Search reports...'; ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                <?php echo getLang('search'); ?>
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- Reports Table -->
                <?php if (empty($reports)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted"><?php echo getLang('no_reports'); ?></p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo getLang('report_title'); ?></th>
                                <th><?php echo getLang('report_type'); ?></th>
                                <th><?php echo getLang('status'); ?></th>
                                <th><?php echo $current_lang === 'ar' ? 'المستخدم' : 'User'; ?></th>
                                <th><?php echo getLang('created_at'); ?></th>
                                <th><?php echo getLang('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reports as $report): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($report['title']); ?></strong>
                                    <?php if ($report['evidence']): ?>
                                    <i class="fas fa-paperclip text-muted ms-1" title="<?php echo $current_lang === 'ar' ? 'مرفق متوفر' : 'Evidence attached'; ?>"></i>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $report['type'] === 'site' ? 'primary' : 
                                            ($report['type'] === 'social' ? 'success' : 'info'); 
                                    ?>">
                                        <?php echo getLang($report['type'] . '_fraud'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $report['status'] === 'pending' ? 'warning' : 
                                            ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                                    ?>">
                                        <?php echo getLang($report['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo $report['username'] ? htmlspecialchars($report['username']) : 'مجهول'; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y-m-d H:i', strtotime($report['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" 
                                                class="btn btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#reportModal<?php echo $report['id']; ?>"
                                                title="<?php echo $current_lang === 'ar' ? 'عرض' : 'View'; ?>">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-outline-warning" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#statusModal<?php echo $report['id']; ?>"
                                                title="<?php echo getLang('edit'); ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-outline-danger" 
                                                onclick="deleteReport(<?php echo $report['id']; ?>)"
                                                title="<?php echo getLang('delete'); ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- Report View Modal -->
                            <div class="modal fade" id="reportModal<?php echo $report['id']; ?>" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title"><?php echo getLang('report_details'); ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <h6><?php echo htmlspecialchars($report['title']); ?></h6>
                                            <p class="text-muted mb-3">
                                                <span class="badge bg-<?php 
                                                    echo $report['type'] === 'site' ? 'primary' : 
                                                        ($report['type'] === 'social' ? 'success' : 'info'); 
                                                ?> me-2">
                                                    <?php echo getLang($report['type'] . '_fraud'); ?>
                                                </span>
                                                <span class="badge bg-<?php 
                                                    echo $report['status'] === 'pending' ? 'warning' : 
                                                        ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                                                ?>">
                                                    <?php echo getLang($report['status']); ?>
                                                </span>
                                            </p>
                                            <p><?php echo nl2br(htmlspecialchars($report['description'])); ?></p>
                                            <?php if ($report['evidence']): ?>
                                            <div class="mb-3">
                                                <strong><?php echo getLang('report_evidence'); ?>:</strong>
                                                <a href="<?php echo htmlspecialchars($report['evidence']); ?>" target="_blank" class="ms-2">
                                                    <?php echo htmlspecialchars($report['evidence']); ?>
                                                </a>
                                            </div>
                                            <?php endif; ?>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <small class="text-muted">
                                                        <strong><?php echo $current_lang === 'ar' ? 'المستخدم:' : 'User:'; ?></strong>
                                                        <?php echo $report['username'] ? htmlspecialchars($report['username']) : 'مجهول'; ?>
                                                    </small>
                                                </div>
                                                <div class="col-md-6">
                                                    <small class="text-muted">
                                                        <strong><?php echo getLang('created_at'); ?>:</strong>
                                                        <?php echo date('Y-m-d H:i:s', strtotime($report['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                <?php echo getLang('close'); ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Status Update Modal -->
                            <div class="modal fade" id="statusModal<?php echo $report['id']; ?>" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form method="POST" action="">
                                            <div class="modal-header">
                                                <h5 class="modal-title"><?php echo getLang('update_status'); ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="update_status">
                                                <input type="hidden" name="report_id" value="<?php echo $report['id']; ?>">
                                                
                                                <div class="mb-3">
                                                    <label for="status<?php echo $report['id']; ?>" class="form-label">
                                                        <?php echo getLang('status'); ?>
                                                    </label>
                                                    <select class="form-select" id="status<?php echo $report['id']; ?>" name="status" required>
                                                        <option value="pending" <?php echo $report['status'] === 'pending' ? 'selected' : ''; ?>>
                                                            <?php echo getLang('pending'); ?>
                                                        </option>
                                                        <option value="reviewed" <?php echo $report['status'] === 'reviewed' ? 'selected' : ''; ?>>
                                                            <?php echo getLang('reviewed'); ?>
                                                        </option>
                                                        <option value="closed" <?php echo $report['status'] === 'closed' ? 'selected' : ''; ?>>
                                                            <?php echo getLang('closed'); ?>
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <?php echo getLang('cancel'); ?>
                                                </button>
                                                <button type="submit" class="btn btn-primary">
                                                    <?php echo getLang('update'); ?>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function deleteReport(reportId) {
    if (confirmDelete()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete';
        
        const reportInput = document.createElement('input');
        reportInput.type = 'hidden';
        reportInput.name = 'report_id';
        reportInput.value = reportId;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generateCSRFToken(); ?>';
        
        form.appendChild(actionInput);
        form.appendChild(reportInput);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

    </div> <!-- End of container -->
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
