<?php
require_once 'config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request';
    } else {
        // Clean and validate input
        $type = cleanInput($_POST['type'] ?? '');
        $title = cleanInput($_POST['title'] ?? '');
        $description = cleanInput($_POST['description'] ?? '');
        $evidence = cleanInput($_POST['evidence'] ?? '');
        
        // Validation
        if (empty($type) || !in_array($type, ['site', 'social', 'phone'])) {
            $errors[] = 'Invalid report type';
        }
        
        if (empty($title) || strlen($title) < 5) {
            $errors[] = 'Title must be at least 5 characters';
        }
        
        if (empty($description) || strlen($description) < 20) {
            $errors[] = 'Description must be at least 20 characters';
        }
        
        // If no errors, save the report
        if (empty($errors)) {
            try {
                $pdo = getDB();
                $stmt = $pdo->prepare("
                    INSERT INTO reports (user_id, type, title, description, evidence, status) 
                    VALUES (?, ?, ?, ?, ?, 'pending')
                ");
                
                $user_id = $_SESSION['user_id'] ?? null;
                $stmt->execute([$user_id, $type, $title, $description, $evidence]);
                
                $success = true;
                $_SESSION['message'] = getLang('report_success');
                $_SESSION['message_type'] = 'success';
                
                // Redirect to home page
                header('Location: index.php');
                exit;
                
            } catch(PDOException $e) {
                $errors[] = getLang('report_error');
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    <?php echo getLang('submit_report'); ?>
                </h4>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <form method="POST" action="" id="reportForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <!-- Report Type -->
                    <div class="mb-3">
                        <label for="type" class="form-label">
                            <i class="fas fa-tag me-1"></i>
                            <?php echo getLang('report_type'); ?> *
                        </label>
                        <select class="form-select" id="type" name="type" required>
                            <option value=""><?php echo getLang('select_type'); ?></option>
                            <option value="site" <?php echo (isset($_POST['type']) && $_POST['type'] === 'site') ? 'selected' : ''; ?>>
                                <?php echo getLang('site_fraud'); ?>
                            </option>
                            <option value="social" <?php echo (isset($_POST['type']) && $_POST['type'] === 'social') ? 'selected' : ''; ?>>
                                <?php echo getLang('social_fraud'); ?>
                            </option>
                            <option value="phone" <?php echo (isset($_POST['type']) && $_POST['type'] === 'phone') ? 'selected' : ''; ?>>
                                <?php echo getLang('phone_fraud'); ?>
                            </option>
                        </select>
                    </div>
                    
                    <!-- Report Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            <?php echo getLang('report_title'); ?> *
                        </label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                               required minlength="5" maxlength="255"
                               placeholder="<?php echo $current_lang === 'ar' ? 'أدخل عنوان البلاغ' : 'Enter report title'; ?>">
                        <div class="form-text">
                            <?php echo $current_lang === 'ar' ? 'يجب أن يكون العنوان واضحاً ومختصراً' : 'Title should be clear and concise'; ?>
                        </div>
                    </div>
                    
                    <!-- Report Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            <?php echo getLang('report_description'); ?> *
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="6" 
                                  required minlength="20" maxlength="2000"
                                  placeholder="<?php echo $current_lang === 'ar' ? 'اشرح تفاصيل البلاغ بالتفصيل' : 'Explain the report details thoroughly'; ?>"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        <div class="form-text">
                            <?php echo $current_lang === 'ar' ? 'اكتب وصفاً مفصلاً للحادثة' : 'Write a detailed description of the incident'; ?>
                        </div>
                    </div>
                    
                    <!-- Evidence -->
                    <div class="mb-3">
                        <label for="evidence" class="form-label">
                            <i class="fas fa-paperclip me-1"></i>
                            <?php echo getLang('report_evidence'); ?>
                        </label>
                        <input type="url" class="form-control" id="evidence" name="evidence" 
                               value="<?php echo htmlspecialchars($_POST['evidence'] ?? ''); ?>"
                               placeholder="<?php echo $current_lang === 'ar' ? 'رابط أو صورة للأدلة (اختياري)' : 'Link or image for evidence (optional)'; ?>">
                        <div class="form-text">
                            <?php echo $current_lang === 'ar' ? 'يمكنك إضافة رابط لصورة أو موقع يحتوي على أدلة' : 'You can add a link to an image or website containing evidence'; ?>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="index.php" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            <?php echo getLang('cancel'); ?>
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>
                            <?php echo getLang('submit'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'نصائح لكتابة بلاغ فعال' : 'Tips for Writing an Effective Report'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><?php echo $current_lang === 'ar' ? 'ما يجب فعله:' : 'Do:' ?></h6>
                        <ul class="text-success">
                            <li><?php echo $current_lang === 'ar' ? 'اكتب وصفاً مفصلاً وواضحاً' : 'Write a detailed and clear description'; ?></li>
                            <li><?php echo $current_lang === 'ar' ? 'أضف روابط أو صور للأدلة' : 'Include links or images as evidence'; ?></li>
                            <li><?php echo $current_lang === 'ar' ? 'اذكر التواريخ والأوقات' : 'Mention dates and times'; ?></li>
                            <li><?php echo $current_lang === 'ar' ? 'كن دقيقاً في المعلومات' : 'Be accurate with information'; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><?php echo $current_lang === 'ar' ? 'ما يجب تجنبه:' : 'Don\'t:' ?></h6>
                        <ul class="text-danger">
                            <li><?php echo $current_lang === 'ar' ? 'لا تذكر معلومات شخصية' : 'Don\'t include personal information'; ?></li>
                            <li><?php echo $current_lang === 'ar' ? 'لا تستخدم لغة مسيئة' : 'Don\'t use offensive language'; ?></li>
                            <li><?php echo $current_lang === 'ar' ? 'لا تقدم ادعاءات كاذبة' : 'Don\'t make false claims'; ?></li>
                            <li><?php echo $current_lang === 'ar' ? 'لا تنسخ بلاغات أخرى' : 'Don\'t copy other reports'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('reportForm').addEventListener('submit', function(e) {
    if (!validateForm('reportForm')) {
        e.preventDefault();
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i><?php echo $current_lang === 'ar' ? 'جاري الإرسال...' : 'Sending...'; ?>';
    submitBtn.disabled = true;
    
    // Re-enable after 5 seconds (in case of error)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>

<?php include 'includes/footer.php'; ?>
