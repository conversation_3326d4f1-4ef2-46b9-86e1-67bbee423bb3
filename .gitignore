# Dependencies
/vendor/
/node_modules/

# Environment files
.env
.env.local
.env.production

# Logs
*.log
/logs/

# Cache
/cache/
*.cache

# Temporary files
*.tmp
*.temp

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Uploaded files (keep directory, ignore contents)
/uploads/*
!/uploads/.gitkeep

# Database backups
*.sql
!database.sql

# Configuration files with sensitive data
config.local.php

# Test coverage
/coverage/

# Build files
/dist/
/build/

# Composer
composer.phar
composer.lock

# NPM
package-lock.json
yarn.lock

# Backup files
*.bak
*.backup
*.old

# Error logs
error_log
access_log
