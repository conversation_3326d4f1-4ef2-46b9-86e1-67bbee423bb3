<?php
require_once '../config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $_SESSION['lang'] ?? DEFAULT_LANG;

// Load language file
include "../lang/{$current_lang}.php";

echo "<h1>Admin Test Page</h1>";
echo "<p>Language: $current_lang</p>";
echo "<p>Session lang: " . ($_SESSION['lang'] ?? 'not set') . "</p>";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    echo "<p style='color: red;'>❌ Not logged in as admin</p>";
    echo "<p>User ID: " . ($_SESSION['user_id'] ?? 'not set') . "</p>";
    echo "<p>User Role: " . ($_SESSION['user_role'] ?? 'not set') . "</p>";
    echo "<p><a href='../login.php'>Go to Login</a></p>";
} else {
    echo "<p style='color: green;'>✅ Logged in as admin</p>";
    echo "<p>User ID: {$_SESSION['user_id']}</p>";
    echo "<p>Username: {$_SESSION['username']}</p>";
    echo "<p>Role: {$_SESSION['user_role']}</p>";
}

// Test database connection
try {
    $pdo = getDB();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check admin user
    $stmt = $pdo->query("SELECT username, role FROM users WHERE username = 'admin'");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin user found: {$admin['username']} (Role: {$admin['role']})</p>";
    } else {
        echo "<p style='color: red;'>❌ Admin user not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../index.php'>Back to Home</a></p>";
?>
