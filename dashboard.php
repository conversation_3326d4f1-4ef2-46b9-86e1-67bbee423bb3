<?php
require_once 'config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// Get user data
$pdo = getDB();
$user_reports = [];
$stats = [];

try {
    // Get user's reports
    $stmt = $pdo->prepare("
        SELECT * FROM reports 
        WHERE user_id = ? 
        ORDER BY created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $user_reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get user statistics
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['total_reports'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ? AND status = 'pending'");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['pending_reports'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ? AND status = 'reviewed'");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['reviewed_reports'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports WHERE user_id = ? AND status = 'closed'");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['closed_reports'] = $stmt->fetchColumn();
    
} catch(PDOException $e) {
    // Handle error silently for now
}

include 'includes/header.php';
?>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm bg-gradient-primary text-white">
            <div class="card-body">
                <h2 class="mb-2">
                    <i class="fas fa-user-circle me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'مرحباً' : 'Welcome'; ?>, <?php echo htmlspecialchars($_SESSION['username']); ?>!
                </h2>
                <p class="mb-0 opacity-75">
                    <?php echo $current_lang === 'ar' 
                        ? 'هذه هي لوحة تحكمك الشخصية. يمكنك من هنا متابعة بلاغاتك وإدارة حسابك.' 
                        : 'This is your personal dashboard. From here you can track your reports and manage your account.'; ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                <h3 class="card-title"><?php echo $stats['total_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('total_reports'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h3 class="card-title"><?php echo $stats['pending_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('pending'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h3 class="card-title"><?php echo $stats['reviewed_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('reviewed'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-times-circle fa-2x text-secondary mb-2"></i>
                <h3 class="card-title"><?php echo $stats['closed_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('closed'); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <a href="submit_report.php" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i>
                        <?php echo getLang('submit_report'); ?>
                    </a>
                    <a href="profile.php" class="btn btn-outline-primary">
                        <i class="fas fa-user-edit me-1"></i>
                        <?php echo $current_lang === 'ar' ? 'تعديل الملف الشخصي' : 'Edit Profile'; ?>
                    </a>
                    <a href="change_password.php" class="btn btn-outline-warning">
                        <i class="fas fa-key me-1"></i>
                        <?php echo $current_lang === 'ar' ? 'تغيير كلمة المرور' : 'Change Password'; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Reports -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'بلاغاتي' : 'My Reports'; ?>
                </h5>
                <a href="submit_report.php" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    <?php echo getLang('submit_report'); ?>
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($user_reports)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted"><?php echo $current_lang === 'ar' ? 'لم تقم بإرسال أي بلاغات بعد' : 'You haven\'t submitted any reports yet'; ?></p>
                    <a href="submit_report.php" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i>
                        <?php echo getLang('submit_report'); ?>
                    </a>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><?php echo getLang('report_title'); ?></th>
                                <th><?php echo getLang('report_type'); ?></th>
                                <th><?php echo getLang('status'); ?></th>
                                <th><?php echo getLang('created_at'); ?></th>
                                <th><?php echo getLang('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_reports as $report): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($report['title']); ?></strong>
                                    <?php if ($report['evidence']): ?>
                                    <i class="fas fa-paperclip text-muted ms-1" title="<?php echo $current_lang === 'ar' ? 'مرفق متوفر' : 'Evidence attached'; ?>"></i>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $report['type'] === 'site' ? 'primary' : 
                                            ($report['type'] === 'social' ? 'success' : 'info'); 
                                    ?>">
                                        <?php echo getLang($report['type'] . '_fraud'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $report['status'] === 'pending' ? 'warning' : 
                                            ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                                    ?>">
                                        <?php echo getLang($report['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y-m-d H:i', strtotime($report['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="view_report.php?id=<?php echo $report['id']; ?>" 
                                           class="btn btn-outline-primary" 
                                           title="<?php echo $current_lang === 'ar' ? 'عرض' : 'View'; ?>">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($report['status'] === 'pending'): ?>
                                        <a href="edit_report.php?id=<?php echo $report['id']; ?>" 
                                           class="btn btn-outline-warning" 
                                           title="<?php echo getLang('edit'); ?>">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php endif; ?>
                                        <button type="button" 
                                                class="btn btn-outline-danger" 
                                                onclick="deleteReport(<?php echo $report['id']; ?>)"
                                                title="<?php echo getLang('delete'); ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'النشاط الأخير' : 'Recent Activity'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($user_reports)): ?>
                <p class="text-muted text-center mb-0">
                    <?php echo $current_lang === 'ar' ? 'لا يوجد نشاط حديث' : 'No recent activity'; ?>
                </p>
                <?php else: ?>
                <div class="timeline">
                    <?php foreach (array_slice($user_reports, 0, 5) as $report): ?>
                    <div class="timeline-item d-flex mb-3">
                        <div class="timeline-marker bg-<?php 
                            echo $report['status'] === 'pending' ? 'warning' : 
                                ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                        ?> rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                        <div class="timeline-content">
                            <p class="mb-1">
                                <strong><?php echo htmlspecialchars($report['title']); ?></strong>
                                <span class="text-muted ms-2">
                                    <?php echo $current_lang === 'ar' ? 'تم إرساله' : 'submitted'; ?>
                                </span>
                            </p>
                            <small class="text-muted">
                                <?php echo date('Y-m-d H:i', strtotime($report['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function deleteReport(reportId) {
    if (confirmDelete()) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete_report.php';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'report_id';
        input.value = reportId;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generateCSRFToken(); ?>';
        
        form.appendChild(input);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
