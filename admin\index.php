<?php
require_once '../config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "../lang/{$current_lang}.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// Get statistics
$pdo = getDB();
$stats = [];

try {
    // Total reports
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports");
    $stats['total_reports'] = $stmt->fetchColumn();
    
    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'user'");
    $stats['total_users'] = $stmt->fetchColumn();
    
    // Pending reports
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'pending'");
    $stats['pending_reports'] = $stmt->fetchColumn();
    
    // Reviewed reports
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'reviewed'");
    $stats['reviewed_reports'] = $stmt->fetchColumn();
    
    // Closed reports
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'closed'");
    $stats['closed_reports'] = $stmt->fetchColumn();
    
    // Reports by type
    $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM reports GROUP BY type");
    $stats['reports_by_type'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recent reports
    $stmt = $pdo->query("
        SELECT r.*, u.username 
        FROM reports r 
        LEFT JOIN users u ON r.user_id = u.id 
        ORDER BY r.created_at DESC 
        LIMIT 5
    ");
    $stats['recent_reports'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recent users
    $stmt = $pdo->query("
        SELECT * FROM users 
        WHERE role = 'user' 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stats['recent_users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(PDOException $e) {
    // Handle error silently for now
}
?>
<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $current_lang === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getLang('admin_panel'); ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap RTL CSS for Arabic -->
    <?php if ($current_lang === 'ar'): ?>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: <?php echo $current_lang === 'ar' ? 'Cairo, Arial, sans-serif' : 'system-ui, sans-serif'; ?>;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-shield-alt me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    <?php echo $_SESSION['username']; ?> (<?php echo getLang('admin_panel'); ?>)
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    <?php echo getLang('logout'); ?>
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">

<!-- Admin Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm bg-dark text-white">
            <div class="card-body">
                <h2 class="mb-2">
                    <i class="fas fa-cog me-2"></i>
                    <?php echo getLang('admin_panel'); ?>
                </h2>
                <p class="mb-0 opacity-75">
                    <?php echo $current_lang === 'ar' 
                        ? 'مرحباً بك في لوحة الإدارة. يمكنك من هنا إدارة البلاغات والمستخدمين والإعدادات.' 
                        : 'Welcome to the admin panel. From here you can manage reports, users, and settings.'; ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                <h3 class="card-title"><?php echo $stats['total_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('total_reports'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-success mb-2"></i>
                <h3 class="card-title"><?php echo $stats['total_users'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('total_users'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h3 class="card-title"><?php echo $stats['pending_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('pending'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                <h3 class="card-title"><?php echo $stats['reviewed_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('reviewed'); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <a href="reports.php" class="btn btn-primary">
                        <i class="fas fa-list-alt me-1"></i>
                        <?php echo getLang('reports'); ?>
                    </a>
                    <a href="users.php" class="btn btn-success">
                        <i class="fas fa-users me-1"></i>
                        <?php echo getLang('users'); ?>
                    </a>
                    <a href="settings.php" class="btn btn-info">
                        <i class="fas fa-cog me-1"></i>
                        <?php echo getLang('settings'); ?>
                    </a>
                    <a href="../index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-1"></i>
                        <?php echo getLang('home'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reports by Type Chart -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'البلاغات حسب النوع' : 'Reports by Type'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['reports_by_type'])): ?>
                <div class="chart-container" style="position: relative; height: 200px;">
                    <canvas id="reportsTypeChart"></canvas>
                </div>
                <?php else: ?>
                <p class="text-muted text-center"><?php echo getLang('no_data'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'البلاغات العاجلة' : 'Urgent Reports'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><?php echo getLang('pending'); ?></span>
                    <span class="badge bg-warning"><?php echo $stats['pending_reports'] ?? 0; ?></span>
                </div>
                <div class="progress mb-3" style="height: 8px;">
                    <div class="progress-bar bg-warning" 
                         style="width: <?php echo $stats['total_reports'] > 0 ? ($stats['pending_reports'] / $stats['total_reports']) * 100 : 0; ?>%"></div>
                </div>
                <a href="reports.php?status=pending" class="btn btn-warning btn-sm">
                    <?php echo $current_lang === 'ar' ? 'عرض البلاغات العاجلة' : 'View Pending Reports'; ?>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'أحدث البلاغات' : 'Recent Reports'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['recent_reports'])): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($stats['recent_reports'] as $report): ?>
                    <div class="list-group-item px-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($report['title']); ?></h6>
                                <small class="text-muted">
                                    <?php echo $report['username'] ? htmlspecialchars($report['username']) : 'مجهول'; ?> • 
                                    <?php echo date('Y-m-d H:i', strtotime($report['created_at'])); ?>
                                </small>
                            </div>
                            <span class="badge bg-<?php 
                                echo $report['status'] === 'pending' ? 'warning' : 
                                    ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                            ?>">
                                <?php echo getLang($report['status']); ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="text-center mt-3">
                    <a href="reports.php" class="btn btn-outline-success btn-sm">
                        <?php echo getLang('view_all'); ?>
                    </a>
                </div>
                <?php else: ?>
                <p class="text-muted text-center"><?php echo getLang('no_data'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'أحدث المستخدمين' : 'Recent Users'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['recent_users'])): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($stats['recent_users'] as $user): ?>
                    <div class="list-group-item px-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($user['username']); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                            </div>
                            <small class="text-muted">
                                <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="text-center mt-3">
                    <a href="users.php" class="btn btn-outline-primary btn-sm">
                        <?php echo getLang('view_all'); ?>
                    </a>
                </div>
                <?php else: ?>
                <p class="text-muted text-center"><?php echo getLang('no_data'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for Reports Type Chart -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
<?php if (!empty($stats['reports_by_type'])): ?>
// Reports Type Chart
const ctx = document.getElementById('reportsTypeChart').getContext('2d');
const reportsTypeChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: [
            <?php foreach ($stats['reports_by_type'] as $type): ?>
            '<?php echo getLang($type['type'] . '_fraud'); ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($stats['reports_by_type'] as $type): ?>
                <?php echo $type['count']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#17a2b8'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
<?php endif; ?>
</script>

    </div> <!-- End of container -->
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
