<?php
require_once 'config.php';

// Check if form is submitted
if ($_POST) {
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    if (empty($new_password)) {
        $error = "كلمة المرور الجديدة مطلوبة";
    } elseif (strlen($new_password) < 6) {
        $error = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
    } elseif ($new_password !== $confirm_password) {
        $error = "كلمتا المرور غير متطابقتين";
    } else {
        try {
            $pdo = getDB();
            
            // Hash the new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // Update admin password
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
            $result = $stmt->execute([$hashed_password]);
            
            if ($result) {
                $success = "تم تغيير كلمة مرور المدير بنجاح!";
                // Clear the form
                $new_password = $confirm_password = '';
            } else {
                $error = "حدث خطأ أثناء تحديث كلمة المرور";
            }
        } catch (Exception $e) {
            $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير كلمة مرور المدير</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h4><i class="fas fa-key me-2"></i>تغيير كلمة مرور المدير</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       value="<?php echo htmlspecialchars($new_password ?? ''); ?>" required>
                                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       value="<?php echo htmlspecialchars($confirm_password ?? ''); ?>" required>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>تغيير كلمة المرور
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-body">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                        <ul class="mb-0">
                            <li>اسم المستخدم: <strong>admin</li>
                            <li>كلمة المرور الحالية: <strong>admin123</strong></li>
                            <li>بعد التغيير، استخدم كلمة المرور الجديدة لتسجيل الدخول</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
