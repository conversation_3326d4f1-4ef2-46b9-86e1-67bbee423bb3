<?php
// Enhanced Database setup script
// This script will create the database and tables with additional features

echo "<h2>Enhanced Database Setup</h2>";

try {
    // Connect to MySQL without specifying database
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', 'as102030.KK');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Connected to MySQL successfully!</p>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS rep CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ Database 'rep' created/verified successfully!</p>";
    
    // Use the database
    $pdo->exec("USE rep");
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) UNIQUE NOT NULL,
            email VARCHAR(150) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('user','admin') DEFAULT 'user',
            last_login TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Users table created/verified successfully!</p>";
    
    // Create reports table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            type ENUM('site','social','phone') NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            evidence VARCHAR(500) NULL,
            status ENUM('pending','reviewed','closed') DEFAULT 'pending',
            priority ENUM('low','medium','high','urgent') DEFAULT 'medium',
            admin_notes TEXT NULL,
            reviewed_by INT NULL,
            reviewed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "<p style='color: green;'>✅ Reports table created/verified successfully!</p>";
    
    // Create languages table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS languages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lang_code VARCHAR(5) NOT NULL,
            key_name VARCHAR(100) NOT NULL,
            value TEXT NOT NULL,
            UNIQUE KEY unique_lang_key (lang_code, key_name)
        )
    ");
    echo "<p style='color: green;'>✅ Languages table created/verified successfully!</p>";
    
    // Create activity_logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            action VARCHAR(100) NOT NULL,
            details TEXT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "<p style='color: green;'>✅ Activity logs table created/verified successfully!</p>";
    
    // Create user_sessions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✅ User sessions table created/verified successfully!</p>";
    
    // Create report_comments table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS report_comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            report_id INT NOT NULL,
            user_id INT NOT NULL,
            comment TEXT NOT NULL,
            is_admin_comment BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✅ Report comments table created/verified successfully!</p>";
    
    // Create notifications table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info','success','warning','danger') DEFAULT 'info',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✅ Notifications table created/verified successfully!</p>";
    
    // Create settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT NOT NULL,
            description TEXT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Settings table created/verified successfully!</p>";
    
    // Check if admin user exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $adminExists = $stmt->fetchColumn();
    
    if (!$adminExists) {
        // Insert default admin user (password: admin123)
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $hashedPassword, 'admin']);
        echo "<p style='color: green;'>✅ Default admin user created!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Admin user already exists</p>";
    }
    
    // Check if language data exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM languages");
    $langCount = $stmt->fetchColumn();
    
    if ($langCount == 0) {
        // Insert enhanced Arabic language data
        $arabicData = [
            ['ar', 'welcome', 'مرحباً بكم في نظام بلاغات الاحتيال'],
            ['ar', 'latest_reports', 'أحدث البلاغات'],
            ['ar', 'submit_report', 'إرسال بلاغ جديد'],
            ['ar', 'login', 'تسجيل الدخول'],
            ['ar', 'register', 'تسجيل جديد'],
            ['ar', 'logout', 'تسجيل الخروج'],
            ['ar', 'dashboard', 'لوحة التحكم'],
            ['ar', 'reports', 'البلاغات'],
            ['ar', 'users', 'المستخدمين'],
            ['ar', 'settings', 'الإعدادات'],
            ['ar', 'admin_panel', 'لوحة الإدارة'],
            ['ar', 'total_reports', 'إجمالي البلاغات'],
            ['ar', 'total_users', 'إجمالي المستخدمين'],
            ['ar', 'pending', 'قيد المراجعة'],
            ['ar', 'reviewed', 'تمت المراجعة'],
            ['ar', 'closed', 'مغلق'],
            ['ar', 'close', 'إغلاق'],
            ['ar', 'update', 'تحديث'],
            ['ar', 'update_status', 'تحديث الحالة'],
            ['ar', 'profile', 'الملف الشخصي'],
            ['ar', 'search', 'بحث'],
            ['ar', 'advanced_search', 'البحث المتقدم'],
            ['ar', 'filter', 'تصفية'],
            ['ar', 'sort', 'ترتيب'],
            ['ar', 'export', 'تصدير'],
            ['ar', 'priority', 'الأولوية'],
            ['ar', 'low', 'منخفضة'],
            ['ar', 'medium', 'متوسطة'],
            ['ar', 'high', 'عالية'],
            ['ar', 'urgent', 'عاجلة'],
            ['ar', 'admin_notes', 'ملاحظات الإدارة'],
            ['ar', 'comments', 'التعليقات'],
            ['ar', 'add_comment', 'إضافة تعليق'],
            ['ar', 'notifications', 'الإشعارات'],
            ['ar', 'mark_read', 'تحديد كمقروء'],
            ['ar', 'account_security', 'أمان الحساب'],
            ['ar', 'delete_account', 'حذف الحساب'],
            ['ar', 'last_login', 'آخر تسجيل دخول'],
            ['ar', 'ip_address', 'عنوان IP'],
            ['ar', 'session_management', 'إدارة الجلسات'],
            ['ar', 'logout_all_sessions', 'تسجيل الخروج من جميع الجلسات']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO languages (lang_code, key_name, value) VALUES (?, ?, ?)");
        foreach ($arabicData as $data) {
            $stmt->execute($data);
        }
        
        // Insert enhanced English language data
        $englishData = [
            ['en', 'welcome', 'Welcome to Fraud Reports System'],
            ['en', 'latest_reports', 'Latest Reports'],
            ['en', 'submit_report', 'Submit New Report'],
            ['en', 'login', 'Login'],
            ['en', 'register', 'Register'],
            ['en', 'logout', 'Logout'],
            ['en', 'dashboard', 'Dashboard'],
            ['en', 'reports', 'Reports'],
            ['en', 'users', 'Users'],
            ['en', 'settings', 'Settings'],
            ['en', 'admin_panel', 'Admin Panel'],
            ['en', 'total_reports', 'Total Reports'],
            ['en', 'total_users', 'Total Users'],
            ['en', 'pending', 'Pending'],
            ['en', 'reviewed', 'Reviewed'],
            ['en', 'closed', 'Closed'],
            ['en', 'close', 'Close'],
            ['en', 'update', 'Update'],
            ['en', 'update_status', 'Update Status'],
            ['en', 'profile', 'Profile'],
            ['en', 'search', 'Search'],
            ['en', 'advanced_search', 'Advanced Search'],
            ['en', 'filter', 'Filter'],
            ['en', 'sort', 'Sort'],
            ['en', 'export', 'Export'],
            ['en', 'priority', 'Priority'],
            ['en', 'low', 'Low'],
            ['en', 'medium', 'Medium'],
            ['en', 'high', 'High'],
            ['en', 'urgent', 'Urgent'],
            ['en', 'admin_notes', 'Admin Notes'],
            ['en', 'comments', 'Comments'],
            ['en', 'add_comment', 'Add Comment'],
            ['en', 'notifications', 'Notifications'],
            ['en', 'mark_read', 'Mark as Read'],
            ['en', 'account_security', 'Account Security'],
            ['en', 'delete_account', 'Delete Account'],
            ['en', 'last_login', 'Last Login'],
            ['en', 'ip_address', 'IP Address'],
            ['en', 'session_management', 'Session Management'],
            ['en', 'logout_all_sessions', 'Logout All Sessions']
        ];
        
        foreach ($englishData as $data) {
            $stmt->execute($data);
        }
        
        echo "<p style='color: green;'>✅ Enhanced language data inserted successfully!</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Language data already exists ($langCount records)</p>";
    }
    
    // Insert default settings
    $defaultSettings = [
        ['site_name', 'نظام بلاغات الاحتيال', 'اسم الموقع'],
        ['site_description', 'نظام متكامل لحماية المستخدمين من الاحتيال عبر الإنترنت', 'وصف الموقع'],
        ['max_file_size', '5242880', 'الحد الأقصى لحجم الملف (5MB)'],
        ['allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', 'أنواع الملفات المسموحة'],
        ['reports_per_page', '20', 'عدد البلاغات في الصفحة'],
        ['auto_approve_reports', 'false', 'الموافقة التلقائية على البلاغات'],
        ['email_notifications', 'true', 'تفعيل إشعارات البريد الإلكتروني'],
        ['maintenance_mode', 'false', 'وضع الصيانة']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    foreach ($defaultSettings as $setting) {
        $stmt->execute($setting);
    }
    echo "<p style='color: green;'>✅ Default settings inserted successfully!</p>";
    
    // Create indexes for better performance
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_reports_status ON reports(status)",
        "CREATE INDEX IF NOT EXISTS idx_reports_type ON reports(type)",
        "CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON activity_logs(action)",
        "CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)",
        "CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $pdo->exec($index);
        } catch (Exception $e) {
            // Index might already exist
        }
    }
    echo "<p style='color: green;'>✅ Database indexes created successfully!</p>";
    
    echo "<hr>";
    echo "<h3>Enhanced Setup Complete!</h3>";
    echo "<p><strong>New Features Added:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Enhanced user management with last login tracking</li>";
    echo "<li>✅ Report priority system</li>";
    echo "<li>✅ Admin notes and comments system</li>";
    echo "<li>✅ Activity logging</li>";
    echo "<li>✅ Session management</li>";
    echo "<li>✅ Notification system</li>";
    echo "<li>✅ Settings management</li>";
    echo "<li>✅ Performance indexes</li>";
    echo "</ul>";
    echo "<p><a href='index.php'>Go to Homepage</a></p>";
    echo "<p><a href='admin/'>Go to Admin Panel</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
