<?php
require_once 'config.php';
require_once 'includes/functions.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

$pdo = getDB();
$reports = [];
$total_results = 0;
$current_page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($current_page - 1) * $per_page;

// Get search parameters
$search = cleanInput($_GET['search'] ?? '');
$status_filter = cleanInput($_GET['status'] ?? '');
$type_filter = cleanInput($_GET['type'] ?? '');
$date_from = cleanInput($_GET['date_from'] ?? '');
$date_to = cleanInput($_GET['date_to'] ?? '');
$sort_by = cleanInput($_GET['sort'] ?? 'created_at');
$sort_order = cleanInput($_GET['order'] ?? 'DESC');

// Validate sort parameters
$allowed_sorts = ['created_at', 'title', 'type', 'status'];
$allowed_orders = ['ASC', 'DESC'];

if (!in_array($sort_by, $allowed_sorts)) $sort_by = 'created_at';
if (!in_array($sort_order, $allowed_orders)) $sort_order = 'DESC';

// Build query
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(r.title LIKE ? OR r.description LIKE ? OR u.username LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "r.status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "r.type = ?";
    $params[] = $type_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(r.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(r.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
try {
    $count_query = "
        SELECT COUNT(*) 
        FROM reports r 
        LEFT JOIN users u ON r.user_id = u.id 
        $where_clause
    ";
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($params);
    $total_results = $stmt->fetchColumn();
} catch(PDOException $e) {
    $total_results = 0;
}

// Get reports with pagination
try {
    $query = "
        SELECT r.*, u.username 
        FROM reports r 
        LEFT JOIN users u ON r.user_id = u.id 
        $where_clause
        ORDER BY r.$sort_by $sort_order
        LIMIT $per_page OFFSET $offset
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $reports = [];
}

$total_pages = ceil($total_results / $per_page);

include 'includes/header.php';
?>

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    <?php echo $current_lang === 'ar' ? 'البحث المتقدم' : 'Advanced Search'; ?>
                </h4>
            </div>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" action="" id="searchForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">
                                <i class="fas fa-search me-1"></i>
                                <?php echo getLang('search'); ?>
                            </label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>"
                                   placeholder="<?php echo $current_lang === 'ar' ? 'البحث في البلاغات...' : 'Search in reports...'; ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-filter me-1"></i>
                                <?php echo getLang('status'); ?>
                            </label>
                            <select class="form-select" id="status" name="status">
                                <option value=""><?php echo getLang('all_statuses'); ?></option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>
                                    <?php echo getLang('pending'); ?>
                                </option>
                                <option value="reviewed" <?php echo $status_filter === 'reviewed' ? 'selected' : ''; ?>>
                                    <?php echo getLang('reviewed'); ?>
                                </option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>
                                    <?php echo getLang('closed'); ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="type" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                <?php echo getLang('report_type'); ?>
                            </label>
                            <select class="form-select" id="type" name="type">
                                <option value=""><?php echo getLang('all_types'); ?></option>
                                <option value="site" <?php echo $type_filter === 'site' ? 'selected' : ''; ?>>
                                    <?php echo getLang('site_fraud'); ?>
                                </option>
                                <option value="social" <?php echo $type_filter === 'social' ? 'selected' : ''; ?>>
                                    <?php echo getLang('social_fraud'); ?>
                                </option>
                                <option value="phone" <?php echo $type_filter === 'phone' ? 'selected' : ''; ?>>
                                    <?php echo getLang('phone_fraud'); ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo $current_lang === 'ar' ? 'من تاريخ' : 'From Date'; ?>
                            </label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo $current_lang === 'ar' ? 'إلى تاريخ' : 'To Date'; ?>
                            </label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="sort" class="form-label">
                                <i class="fas fa-sort me-1"></i>
                                <?php echo $current_lang === 'ar' ? 'ترتيب حسب' : 'Sort By'; ?>
                            </label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="created_at" <?php echo $sort_by === 'created_at' ? 'selected' : ''; ?>>
                                    <?php echo getLang('created_at'); ?>
                                </option>
                                <option value="title" <?php echo $sort_by === 'title' ? 'selected' : ''; ?>>
                                    <?php echo getLang('report_title'); ?>
                                </option>
                                <option value="type" <?php echo $sort_by === 'type' ? 'selected' : ''; ?>>
                                    <?php echo getLang('report_type'); ?>
                                </option>
                                <option value="status" <?php echo $sort_by === 'status' ? 'selected' : ''; ?>>
                                    <?php echo getLang('status'); ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-1">
                            <label for="order" class="form-label">
                                <i class="fas fa-arrows-alt-v me-1"></i>
                                <?php echo $current_lang === 'ar' ? 'ترتيب' : 'Order'; ?>
                            </label>
                            <select class="form-select" id="order" name="order">
                                <option value="DESC" <?php echo $sort_order === 'DESC' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'ar' ? 'تنازلي' : 'Desc'; ?>
                                </option>
                                <option value="ASC" <?php echo $sort_order === 'ASC' ? 'selected' : ''; ?>>
                                    <?php echo $current_lang === 'ar' ? 'تصاعدي' : 'Asc'; ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>
                                    <?php echo getLang('search'); ?>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                    <i class="fas fa-times me-2"></i>
                                    <?php echo $current_lang === 'ar' ? 'مسح' : 'Clear'; ?>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="exportResults()">
                                    <i class="fas fa-download me-2"></i>
                                    <?php echo $current_lang === 'ar' ? 'تصدير' : 'Export'; ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Results Summary -->
<?php if ($search || $status_filter || $type_filter || $date_from || $date_to): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong><?php echo $total_results; ?></strong> 
            <?php echo $current_lang === 'ar' ? 'نتيجة' : 'results'; ?> 
            <?php echo $current_lang === 'ar' ? 'تم العثور عليها' : 'found'; ?>
            
            <?php if ($search): ?>
                <?php echo $current_lang === 'ar' ? 'للبحث عن' : 'for'; ?> 
                <strong>"<?php echo htmlspecialchars($search); ?>"</strong>
            <?php endif; ?>
            
            <?php if ($status_filter): ?>
                <?php echo $current_lang === 'ar' ? 'مع الحالة' : 'with status'; ?> 
                <strong><?php echo getLang($status_filter); ?></strong>
            <?php endif; ?>
            
            <?php if ($type_filter): ?>
                <?php echo $current_lang === 'ar' ? 'من نوع' : 'of type'; ?> 
                <strong><?php echo getLang($type_filter . '_fraud'); ?></strong>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Search Results -->
<?php if (empty($reports)): ?>
<div class="text-center py-5">
    <i class="fas fa-search fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">
        <?php echo $current_lang === 'ar' ? 'لا توجد نتائج' : 'No results found'; ?>
    </h5>
    <p class="text-muted">
        <?php echo $current_lang === 'ar' 
            ? 'جرب تغيير معايير البحث أو استخدام كلمات بحث مختلفة' 
            : 'Try changing your search criteria or using different search terms'; ?>
    </p>
</div>
<?php else: ?>
<!-- Results Table -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th><?php echo getLang('report_title'); ?></th>
                        <th><?php echo getLang('report_type'); ?></th>
                        <th><?php echo getLang('status'); ?></th>
                        <th><?php echo $current_lang === 'ar' ? 'المستخدم' : 'User'; ?></th>
                        <th><?php echo getLang('created_at'); ?></th>
                        <th><?php echo getLang('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reports as $report): ?>
                    <tr>
                        <td>
                            <strong><?php echo htmlspecialchars($report['title']); ?></strong>
                            <?php if ($report['evidence']): ?>
                            <i class="fas fa-paperclip text-muted ms-1" title="<?php echo $current_lang === 'ar' ? 'مرفق متوفر' : 'Evidence attached'; ?>"></i>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?php 
                                echo $report['type'] === 'site' ? 'primary' : 
                                    ($report['type'] === 'social' ? 'success' : 'info'); 
                            ?>">
                                <?php echo getLang($report['type'] . '_fraud'); ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-<?php 
                                echo $report['status'] === 'pending' ? 'warning' : 
                                    ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                            ?>">
                                <?php echo getLang($report['status']); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo $report['username'] ? htmlspecialchars($report['username']) : 'مجهول'; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo timeAgo($report['created_at']); ?>
                            </small>
                        </td>
                        <td>
                            <a href="view_report.php?id=<?php echo $report['id']; ?>" 
                               class="btn btn-sm btn-outline-primary" title="<?php echo getLang('view'); ?>">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Search results pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($current_page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page - 1])); ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($current_page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page + 1])); ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<script>
function clearForm() {
    document.getElementById('searchForm').reset();
    window.location.href = 'search.php';
}

function exportResults() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.open('export_reports.php?' + params.toString(), '_blank');
}

// Auto-submit form on select change
document.getElementById('status').addEventListener('change', function() {
    document.getElementById('searchForm').submit();
});

document.getElementById('type').addEventListener('change', function() {
    document.getElementById('searchForm').submit();
});

document.getElementById('sort').addEventListener('change', function() {
    document.getElementById('searchForm').submit();
});

document.getElementById('order').addEventListener('change', function() {
    document.getElementById('searchForm').submit();
});
</script>

<?php include 'includes/footer.php'; ?>
