{"name": "fraud-reports-system", "description": "A comprehensive system for managing online fraud reports built with PHP and MySQL", "type": "project", "keywords": ["fraud", "reports", "php", "mysql", "bootstrap", "arabic", "english"], "license": "MIT", "authors": [{"name": "Fraud Reports System", "email": "<EMAIL>"}], "require": {"php": ">=8.0", "ext-pdo": "*", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"FraudReports\\": "src/"}}, "autoload-dev": {"psr-4": {"FraudReports\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "post-install-cmd": ["chmod 755 uploads/", "chmod 644 *.php"]}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}