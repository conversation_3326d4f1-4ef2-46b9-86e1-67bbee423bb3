<?php
require_once '../config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    if ($user_id > 0) {
        $pdo = getDB();
        
        switch ($action) {
            case 'update_status':
                $status = $_POST['status'];
                $stmt = $pdo->prepare("UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$status, $user_id]);
                
                // Log activity
                logActivity($_SESSION['user_id'], 'user_status_updated', "Updated user ID $user_id status to $status");
                break;
                
            case 'update_role':
                $role = $_POST['role'];
                $stmt = $pdo->prepare("UPDATE users SET role = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$role, $user_id]);
                
                // Log activity
                logActivity($_SESSION['user_id'], 'user_role_updated', "Updated user ID $user_id role to $role");
                break;
                
            case 'delete_user':
                // Don't allow admin to delete themselves
                if ($user_id === $_SESSION['user_id']) {
                    $error_message = "لا يمكنك حذف حسابك الخاص";
                    break;
                }
                
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                
                // Log activity
                logActivity($_SESSION['user_id'], 'user_deleted', "Deleted user ID $user_id");
                break;
        }
    }
}

// Get users with pagination and search
$pdo = getDB();

// Search and filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$role_filter = $_GET['role'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($role_filter) {
    $where_conditions[] = "role = ?";
    $params[] = $role_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_sql = "SELECT COUNT(*) FROM users $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_users = $count_stmt->fetchColumn();
$total_pages = ceil($total_users / $per_page);

// Get users
$sql = "SELECT u.*, 
               COUNT(r.id) as report_count,
               MAX(r.created_at) as last_report_date
        FROM users u 
        LEFT JOIN reports r ON u.id = r.user_id 
        $where_clause 
        GROUP BY u.id 
        ORDER BY u.created_at DESC 
        LIMIT ? OFFSET ?";

$params[] = $per_page;
$params[] = $offset;

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get status and role options for filters
$statuses = ['active', 'inactive', 'suspended', 'pending'];
$roles = ['user', 'moderator', 'admin'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة الإدارة</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .admin-nav {
            background-color: #343a40;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .admin-nav .nav-link {
            color: #adb5bd;
            transition: color 0.3s;
        }
        
        .admin-nav .nav-link:hover,
        .admin-nav .nav-link.active {
            color: white;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .role-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .search-form {
            background-color: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 2rem;
        }
        
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="mb-0 opacity-75">إدارة حسابات المستخدمين في النظام</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="index.php" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Navigation -->
    <nav class="admin-nav">
        <div class="container">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-file-alt me-2"></i>
                        البلاغات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="users.php">
                        <i class="fas fa-users me-2"></i>
                        المستخدمين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <!-- Search and Filter Form -->
        <div class="search-form">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= htmlspecialchars($search) ?>" 
                           placeholder="اسم المستخدم، البريد الإلكتروني، الاسم الكامل">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>نشط</option>
                        <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                        <option value="suspended" <?= $status_filter === 'suspended' ? 'selected' : '' ?>>معلق</option>
                        <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="role" class="form-label">الدور</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">جميع الأدوار</option>
                        <option value="user" <?= $role_filter === 'user' ? 'selected' : '' ?>>مستخدم</option>
                        <option value="moderator" <?= $role_filter === 'moderator' ? 'selected' : '' ?>>مشرف</option>
                        <option value="admin" <?= $role_filter === 'admin' ? 'selected' : '' ?>>مدير</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                        <a href="users.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            مسح
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين (<?= number_format($total_users) ?>)
                </h5>
                <div>
                    <span class="badge bg-primary"><?= $total_users ?> مستخدم</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>المستخدم</th>
                                <th>معلومات الاتصال</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>البلاغات</th>
                                <th>تاريخ التسجيل</th>
                                <th>آخر تسجيل دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4 text-muted">
                                        <i class="fas fa-users fa-2x mb-3 d-block"></i>
                                        لا يوجد مستخدمين
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-3">
                                                    <?= strtoupper(substr($user['username'], 0, 1)) ?>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?= htmlspecialchars($user['full_name'] ?: $user['username']) ?></div>
                                                    <small class="text-muted">@<?= htmlspecialchars($user['username']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="small">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    <?= htmlspecialchars($user['email']) ?>
                                                </div>
                                                <?php if ($user['phone']): ?>
                                                    <div class="small text-muted">
                                                        <i class="fas fa-phone me-1"></i>
                                                        <?= htmlspecialchars($user['phone']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $role_colors = [
                                                'admin' => 'danger',
                                                'moderator' => 'warning',
                                                'user' => 'secondary'
                                            ];
                                            $role_labels = [
                                                'admin' => 'مدير',
                                                'moderator' => 'مشرف',
                                                'user' => 'مستخدم'
                                            ];
                                            ?>
                                            <span class="badge bg-<?= $role_colors[$user['role']] ?> role-badge">
                                                <?= $role_labels[$user['role']] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                'active' => 'success',
                                                'inactive' => 'secondary',
                                                'suspended' => 'danger',
                                                'pending' => 'warning'
                                            ];
                                            $status_labels = [
                                                'active' => 'نشط',
                                                'inactive' => 'غير نشط',
                                                'suspended' => 'معلق',
                                                'pending' => 'في الانتظار'
                                            ];
                                            ?>
                                            <span class="badge bg-<?= $status_colors[$user['status']] ?> status-badge">
                                                <?= $status_labels[$user['status']] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <div class="fw-bold"><?= $user['report_count'] ?></div>
                                                <?php if ($user['last_report_date']): ?>
                                                    <small class="text-muted">
                                                        آخر: <?= date('Y/m/d', strtotime($user['last_report_date'])) ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('Y/m/d', strtotime($user['created_at'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($user['last_login']): ?>
                                                <small class="text-muted">
                                                    <?= date('Y/m/d H:i', strtotime($user['last_login'])) ?>
                                                </small>
                                            <?php else: ?>
                                                <small class="text-muted">لم يسجل دخول</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#editUserModal<?= $user['id'] ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#userDetailsModal<?= $user['id'] ?>">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="confirmDelete(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="صفحات المستخدمين">
                <ul class="pagination">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <!-- Edit User Modals -->
    <?php foreach ($users as $user): ?>
        <div class="modal fade" id="editUserModal<?= $user['id'] ?>" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تعديل المستخدم: <?= htmlspecialchars($user['username']) ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="update_status">
                            <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                            
                            <div class="mb-3">
                                <label for="status<?= $user['id'] ?>" class="form-label">الحالة</label>
                                <select class="form-select" id="status<?= $user['id'] ?>" name="status" required>
                                    <option value="active" <?= $user['status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                                    <option value="inactive" <?= $user['status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                    <option value="suspended" <?= $user['status'] === 'suspended' ? 'selected' : '' ?>>معلق</option>
                                    <option value="pending" <?= $user['status'] === 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="role<?= $user['id'] ?>" class="form-label">الدور</label>
                                <select class="form-select" id="role<?= $user['id'] ?>" name="role" required>
                                    <option value="user" <?= $user['role'] === 'user' ? 'selected' : '' ?>>مستخدم</option>
                                    <option value="moderator" <?= $user['role'] === 'moderator' ? 'selected' : '' ?>>مشرف</option>
                                    <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>مدير</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- User Details Modals -->
    <?php foreach ($users as $user): ?>
        <div class="modal fade" id="userDetailsModal<?= $user['id'] ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل المستخدم: <?= htmlspecialchars($user['username']) ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المعلومات الأساسية</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>اسم المستخدم:</strong></td>
                                        <td><?= htmlspecialchars($user['username']) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الاسم الكامل:</strong></td>
                                        <td><?= htmlspecialchars($user['full_name'] ?: 'غير محدد') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>البريد الإلكتروني:</strong></td>
                                        <td><?= htmlspecialchars($user['email']) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>رقم الهاتف:</strong></td>
                                        <td><?= htmlspecialchars($user['phone'] ?: 'غير محدد') ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات الحساب</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>الدور:</strong></td>
                                        <td>
                                            <span class="badge bg-<?= $role_colors[$user['role']] ?>">
                                                <?= $role_labels[$user['role']] ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>
                                            <span class="badge bg-<?= $status_colors[$user['status']] ?>">
                                                <?= $status_labels[$user['status']] ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ التسجيل:</strong></td>
                                        <td><?= date('Y/m/d H:i:s', strtotime($user['created_at'])) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>آخر تسجيل دخول:</strong></td>
                                        <td>
                                            <?= $user['last_login'] ? date('Y/m/d H:i:s', strtotime($user['last_login'])) : 'لم يسجل دخول' ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>إحصائيات البلاغات</h6>
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-primary"><?= $user['report_count'] ?></h4>
                                            <small>إجمالي البلاغات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-success"><?= $user['status'] === 'active' ? 'نشط' : 'غير نشط' ?></h4>
                                            <small>حالة الحساب</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h4 class="text-info"><?= $role_labels[$user['role']] ?></h4>
                                            <small>الدور في النظام</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المستخدم: <strong id="deleteUserName"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_user">
                        <input type="hidden" name="user_id" id="deleteUserId">
                        <button type="submit" class="btn btn-danger">حذف المستخدم</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function confirmDelete(userId, username) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUserName').textContent = username;
            new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
        }
        
        // Auto-refresh page after successful actions
        <?php if (isset($_POST['action'])): ?>
            setTimeout(function() {
                window.location.reload();
            }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
