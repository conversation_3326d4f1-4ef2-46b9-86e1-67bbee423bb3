<?php
require_once 'config.php';

// Get current language
$current_lang = $_GET['lang'] ?? $_SESSION['lang'] ?? DEFAULT_LANG;
$_SESSION['lang'] = $current_lang;

// Load language file
include "lang/{$current_lang}.php";

// Get latest reports
$pdo = getDB();
$latest_reports = [];

try {
    $stmt = $pdo->prepare("
        SELECT r.*, u.username 
        FROM reports r 
        LEFT JOIN users u ON r.user_id = u.id 
        ORDER BY r.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $latest_reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // Handle error silently for now
}

// Get statistics
$stats = [];
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports");
    $stats['total_reports'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'user'");
    $stats['total_users'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM reports WHERE status = 'pending'");
    $stats['pending_reports'] = $stmt->fetchColumn();
} catch(PDOException $e) {
    // Handle error silently for now
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<div class="row mb-5">
    <div class="col-lg-8 mx-auto text-center">
        <h1 class="display-4 fw-bold text-primary mb-3">
            <i class="fas fa-shield-alt me-3"></i>
            <?php echo getLang('welcome'); ?>
        </h1>
        <p class="lead mb-4">
            <?php echo $current_lang === 'ar' 
                ? 'نظام متكامل لحماية المستخدمين من الاحتيال عبر الإنترنت. ساعد الآخرين من خلال الإبلاغ عن المواقع والحسابات المشبوهة.' 
                : 'A comprehensive system to protect users from online fraud. Help others by reporting suspicious websites and accounts.'; ?>
        </p>
        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
            <a href="submit_report.php" class="btn btn-primary btn-lg px-4 me-md-2">
                <i class="fas fa-plus-circle me-2"></i>
                <?php echo getLang('submit_report'); ?>
            </a>
            <?php if (!isset($_SESSION['user_id'])): ?>
            <a href="register.php" class="btn btn-outline-primary btn-lg px-4">
                <i class="fas fa-user-plus me-2"></i>
                <?php echo getLang('register'); ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="row mb-5">
    <div class="col-md-4 text-center">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
                <h3 class="card-title"><?php echo $stats['total_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('total_reports'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <i class="fas fa-users fa-3x text-success mb-3"></i>
                <h3 class="card-title"><?php echo $stats['total_users'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('total_users'); ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                <h3 class="card-title"><?php echo $stats['pending_reports'] ?? 0; ?></h3>
                <p class="card-text text-muted"><?php echo getLang('pending'); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Latest Reports Section -->
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-list-alt me-2"></i>
            <?php echo getLang('latest_reports'); ?>
        </h2>
        
        <?php if (empty($latest_reports)): ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <p class="text-muted"><?php echo getLang('no_reports'); ?></p>
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($latest_reports as $report): ?>
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card report-card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <span class="badge bg-<?php 
                                echo $report['type'] === 'site' ? 'primary' : 
                                    ($report['type'] === 'social' ? 'success' : 'info'); 
                            ?>">
                                <?php echo getLang($report['type'] . '_fraud'); ?>
                            </span>
                            <span class="badge bg-<?php 
                                echo $report['status'] === 'pending' ? 'warning' : 
                                    ($report['status'] === 'reviewed' ? 'success' : 'secondary'); 
                            ?> status-badge">
                                <?php echo getLang($report['status']); ?>
                            </span>
                        </div>
                        
                        <h5 class="card-title"><?php echo htmlspecialchars($report['title']); ?></h5>
                        <p class="card-text text-muted">
                            <?php echo substr(htmlspecialchars($report['description']), 0, 100); ?>
                            <?php if (strlen($report['description']) > 100): ?>...<?php endif; ?>
                        </p>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                <?php echo $report['username'] ? htmlspecialchars($report['username']) : 'مجهول'; ?>
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('Y-m-d', strtotime($report['created_at'])); ?>
                            </small>
                        </div>
                        
                        <?php if ($report['evidence']): ?>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-paperclip me-1"></i>
                                <?php echo $current_lang === 'ar' ? 'مرفق متوفر' : 'Evidence attached'; ?>
                            </small>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Share Buttons -->
                        <div class="share-buttons">
                            <button class="btn btn-sm btn-outline-primary share-btn" 
                                    onclick="shareReport('<?php echo htmlspecialchars($report['title']); ?>', '<?php echo SITE_URL; ?>/view_report.php?id=<?php echo $report['id']; ?>', 'whatsapp')">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info share-btn" 
                                    onclick="shareReport('<?php echo htmlspecialchars($report['title']); ?>', '<?php echo SITE_URL; ?>/view_report.php?id=<?php echo $report['id']; ?>', 'twitter')">
                                <i class="fab fa-twitter"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary share-btn" 
                                    onclick="shareReport('<?php echo htmlspecialchars($report['title']); ?>', '<?php echo SITE_URL; ?>/view_report.php?id=<?php echo $report['id']; ?>', 'facebook')">
                                <i class="fab fa-facebook"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary share-btn" 
                                    onclick="copyLink('<?php echo SITE_URL; ?>/view_report.php?id=<?php echo $report['id']; ?>')">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="all_reports.php" class="btn btn-outline-primary">
                <?php echo getLang('view_all'); ?>
                <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Features Section -->
<div class="row mt-5">
    <div class="col-12">
        <h2 class="text-center mb-5">
            <?php echo $current_lang === 'ar' ? 'مميزات النظام' : 'System Features'; ?>
        </h2>
    </div>
    
    <div class="col-md-4 text-center mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                <h5 class="card-title">
                    <?php echo $current_lang === 'ar' ? 'حماية شاملة' : 'Comprehensive Protection'; ?>
                </h5>
                <p class="card-text">
                    <?php echo $current_lang === 'ar' 
                        ? 'حماية من جميع أنواع الاحتيال عبر الإنترنت' 
                        : 'Protection from all types of online fraud'; ?>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 text-center mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <i class="fas fa-globe fa-3x text-success mb-3"></i>
                <h5 class="card-title">
                    <?php echo $current_lang === 'ar' ? 'دعم متعدد اللغات' : 'Multi-Language Support'; ?>
                </h5>
                <p class="card-text">
                    <?php echo $current_lang === 'ar' 
                        ? 'دعم اللغة العربية والإنجليزية' 
                        : 'Support for Arabic and English languages'; ?>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 text-center mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <i class="fas fa-mobile-alt fa-3x text-info mb-3"></i>
                <h5 class="card-title">
                    <?php echo $current_lang === 'ar' ? 'تصميم متجاوب' : 'Responsive Design'; ?>
                </h5>
                <p class="card-text">
                    <?php echo $current_lang === 'ar' 
                        ? 'يعمل على جميع الأجهزة والشاشات' 
                        : 'Works on all devices and screen sizes'; ?>
                </p>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
