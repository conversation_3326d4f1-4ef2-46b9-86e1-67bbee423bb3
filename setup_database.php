<?php
// Database setup script
// This script will create the database and tables if they don't exist

echo "<h2>Database Setup</h2>";

try {
    // Connect to MySQL without specifying database
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', 'as102030.KK');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Connected to MySQL successfully!</p>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS rep CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ Database 'rep' created/verified successfully!</p>";
    
    // Use the database
    $pdo->exec("USE rep");
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) UNIQUE NOT NULL,
            email VARCHAR(150) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('user','admin') DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Users table created/verified successfully!</p>";
    
    // Create reports table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            type ENUM('site','social','phone') NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            evidence VARCHAR(500) NULL,
            status ENUM('pending','reviewed','closed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "<p style='color: green;'>✅ Reports table created/verified successfully!</p>";
    
    // Create languages table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS languages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lang_code VARCHAR(5) NOT NULL,
            key_name VARCHAR(100) NOT NULL,
            value TEXT NOT NULL,
            UNIQUE KEY unique_lang_key (lang_code, key_name)
        )
    ");
    echo "<p style='color: green;'>✅ Languages table created/verified successfully!</p>";
    
    // Check if admin user exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $adminExists = $stmt->fetchColumn();
    
    if (!$adminExists) {
        // Insert default admin user (password: admin123)
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $hashedPassword, 'admin']);
        echo "<p style='color: green;'>✅ Default admin user created!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Admin user already exists</p>";
    }
    
    // Check if language data exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM languages");
    $langCount = $stmt->fetchColumn();
    
    if ($langCount == 0) {
        // Insert Arabic language data
        $arabicData = [
            ['ar', 'welcome', 'مرحباً بكم في نظام بلاغات الاحتيال'],
            ['ar', 'latest_reports', 'أحدث البلاغات'],
            ['ar', 'submit_report', 'إرسال بلاغ جديد'],
            ['ar', 'login', 'تسجيل الدخول'],
            ['ar', 'register', 'تسجيل جديد'],
            ['ar', 'logout', 'تسجيل الخروج'],
            ['ar', 'dashboard', 'لوحة التحكم'],
            ['ar', 'reports', 'البلاغات'],
            ['ar', 'users', 'المستخدمين'],
            ['ar', 'settings', 'الإعدادات'],
            ['ar', 'admin_panel', 'لوحة الإدارة'],
            ['ar', 'total_reports', 'إجمالي البلاغات'],
            ['ar', 'total_users', 'إجمالي المستخدمين'],
            ['ar', 'pending', 'قيد المراجعة'],
            ['ar', 'reviewed', 'تمت المراجعة'],
            ['ar', 'closed', 'مغلق'],
            ['ar', 'close', 'إغلاق'],
            ['ar', 'update', 'تحديث'],
            ['ar', 'update_status', 'تحديث الحالة'],
            ['ar', 'profile', 'الملف الشخصي']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO languages (lang_code, key_name, value) VALUES (?, ?, ?)");
        foreach ($arabicData as $data) {
            $stmt->execute($data);
        }
        
        // Insert English language data
        $englishData = [
            ['en', 'welcome', 'Welcome to Fraud Reports System'],
            ['en', 'latest_reports', 'Latest Reports'],
            ['en', 'submit_report', 'Submit New Report'],
            ['en', 'login', 'Login'],
            ['en', 'register', 'Register'],
            ['en', 'logout', 'Logout'],
            ['en', 'dashboard', 'Dashboard'],
            ['en', 'reports', 'Reports'],
            ['en', 'users', 'Users'],
            ['en', 'settings', 'Settings'],
            ['en', 'admin_panel', 'Admin Panel'],
            ['en', 'total_reports', 'Total Reports'],
            ['en', 'total_users', 'Total Users'],
            ['en', 'pending', 'Pending'],
            ['en', 'reviewed', 'Reviewed'],
            ['en', 'closed', 'Closed'],
            ['en', 'close', 'Close'],
            ['en', 'update', 'Update'],
            ['en', 'update_status', 'Update Status'],
            ['en', 'profile', 'Profile']
        ];
        
        foreach ($englishData as $data) {
            $stmt->execute($data);
        }
        
        echo "<p style='color: green;'>✅ Language data inserted successfully!</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Language data already exists ($langCount records)</p>";
    }
    
    echo "<hr>";
    echo "<h3>Setup Complete!</h3>";
    echo "<p><a href='index.php'>Go to Homepage</a></p>";
    echo "<p><a href='admin/'>Go to Admin Panel</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
