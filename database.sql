-- Create Database
CREATE DATABASE IF NOT EXISTS rep CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE rep;

-- Users Table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user','admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reports Table
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    type ENUM('site','social','phone') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    evidence VARCHAR(500) NULL,
    status ENUM('pending','reviewed','closed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Languages Table
CREATE TABLE languages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lang_code VARCHAR(5) NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    value TEXT NOT NULL,
    UNIQUE KEY unique_lang_key (lang_code, key_name)
);

-- Insert Default Admin User (password: admin123)
INSERT INTO users (username, email, password, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert Arabic Language Data
INSERT INTO languages (lang_code, key_name, value) VALUES
('ar', 'welcome', 'مرحباً بكم في نظام بلاغات الاحتيال'),
('ar', 'latest_reports', 'أحدث البلاغات'),
('ar', 'submit_report', 'إرسال بلاغ جديد'),
('ar', 'login', 'تسجيل الدخول'),
('ar', 'register', 'تسجيل جديد'),
('ar', 'logout', 'تسجيل الخروج'),
('ar', 'dashboard', 'لوحة التحكم'),
('ar', 'reports', 'البلاغات'),
('ar', 'users', 'المستخدمين'),
('ar', 'settings', 'الإعدادات'),
('ar', 'report_type', 'نوع البلاغ'),
('ar', 'report_title', 'عنوان البلاغ'),
('ar', 'report_description', 'وصف البلاغ'),
('ar', 'report_evidence', 'أدلة البلاغ'),
('ar', 'submit', 'إرسال'),
('ar', 'cancel', 'إلغاء'),
('ar', 'edit', 'تعديل'),
('ar', 'delete', 'حذف'),
('ar', 'status', 'الحالة'),
('ar', 'pending', 'قيد المراجعة'),
('ar', 'reviewed', 'تمت المراجعة'),
('ar', 'closed', 'مغلق'),
('ar', 'created_at', 'تاريخ الإنشاء'),
('ar', 'actions', 'الإجراءات'),
('ar', 'no_reports', 'لا توجد بلاغات'),
('ar', 'site_fraud', 'احتيال موقع'),
('ar', 'social_fraud', 'احتيال حساب اجتماعي'),
('ar', 'phone_fraud', 'احتيال هاتف'),
('ar', 'username', 'اسم المستخدم'),
('ar', 'email', 'البريد الإلكتروني'),
('ar', 'password', 'كلمة المرور'),
('ar', 'confirm_password', 'تأكيد كلمة المرور'),
('ar', 'home', 'الرئيسية'),
('ar', 'about', 'حول النظام'),
('ar', 'contact', 'اتصل بنا'),
('ar', 'language', 'اللغة'),
('ar', 'arabic', 'العربية'),
('ar', 'english', 'الإنجليزية');

-- Insert English Language Data
INSERT INTO languages (lang_code, key_name, value) VALUES
('en', 'welcome', 'Welcome to Fraud Reports System'),
('en', 'latest_reports', 'Latest Reports'),
('en', 'submit_report', 'Submit New Report'),
('en', 'login', 'Login'),
('en', 'register', 'Register'),
('en', 'logout', 'Logout'),
('en', 'dashboard', 'Dashboard'),
('en', 'reports', 'Reports'),
('en', 'users', 'Users'),
('en', 'settings', 'Settings'),
('en', 'report_type', 'Report Type'),
('en', 'report_title', 'Report Title'),
('en', 'report_description', 'Report Description'),
('en', 'report_evidence', 'Report Evidence'),
('en', 'submit', 'Submit'),
('en', 'cancel', 'Cancel'),
('en', 'edit', 'Edit'),
('en', 'delete', 'Delete'),
('en', 'status', 'Status'),
('en', 'pending', 'Pending'),
('en', 'reviewed', 'Reviewed'),
('en', 'closed', 'Closed'),
('en', 'created_at', 'Created At'),
('en', 'actions', 'Actions'),
('en', 'no_reports', 'No reports found'),
('en', 'site_fraud', 'Website Fraud'),
('en', 'social_fraud', 'Social Media Fraud'),
('en', 'phone_fraud', 'Phone Fraud'),
('en', 'username', 'Username'),
('en', 'email', 'Email'),
('en', 'password', 'Password'),
('en', 'confirm_password', 'Confirm Password'),
('en', 'home', 'Home'),
('en', 'about', 'About'),
('en', 'contact', 'Contact'),
('en', 'language', 'Language'),
('en', 'arabic', 'Arabic'),
('en', 'english', 'English');
