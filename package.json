{"name": "fraud-reports-system", "version": "1.0.0", "description": "A comprehensive system for managing online fraud reports", "main": "index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "test": "jest"}, "keywords": ["fraud", "reports", "php", "mysql", "bootstrap", "arabic", "english"], "author": "Fraud Reports System", "license": "MIT", "devDependencies": {"webpack": "^5.0.0", "webpack-cli": "^4.0.0", "babel-loader": "^8.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "css-loader": "^6.0.0", "style-loader": "^3.0.0", "jest": "^27.0.0"}, "dependencies": {"bootstrap": "^5.3.0", "@fortawesome/fontawesome-free": "^6.4.0", "chart.js": "^4.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}