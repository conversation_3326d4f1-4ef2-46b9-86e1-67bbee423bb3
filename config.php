<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'rep');
define('DB_USER', 'root');
define('DB_PASS', 'as102030.KK');

// Site Configuration
define('SITE_NAME', 'نظام بلاغات الاحتيال');
define('SITE_URL', 'http://localhost/rep');
define('DEFAULT_LANG', 'ar');

// Session Configuration
session_start();

// Database Connection
function getDB() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Language Function
function getLang($key) {
    global $lang;
    return isset($lang[$key]) ? $lang[$key] : $key;
}

// Security Functions - Now included from includes/functions.php
    
    // Include common functions
    require_once __DIR__ . '/includes/functions.php';
?>
